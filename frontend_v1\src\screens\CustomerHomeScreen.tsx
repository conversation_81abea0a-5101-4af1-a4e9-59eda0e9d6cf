import React, { useCallback, useMemo, useEffect } from 'react';
import { ScrollView, View, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { StyleSheet, RefreshControl, SafeAreaView, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Import components
import { FocusableButton } from '../components/accessibility/FocusableButton';
import { Heading } from '../components/typography/Typography';
import { ErrorDisplay } from '../components/error/ErrorDisplay';
import { EnhancedErrorBoundary } from '../components/error/EnhancedErrorBoundary';

// Import hooks and services
import { useCustomerHomeData } from '../hooks/useCustomerHomeData';
import { useAuthStore } from '../store/authSlice';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigationGuard } from '../hooks/useNavigationGuard';
import { usePerformance, useLifecyclePerformance } from '../hooks/usePerformance';
import { useErrorHandling } from '../hooks/useErrorHandling';

// Import types and utilities
import { ScreenReaderUtils } from '../utils/accessibilityUtils';
import type { ServiceCategory, FeaturedProvider } from '../services/customerService';

// Import responsive utilities for hyper-minimalist design
const getResponsiveSpacing = (base: number) => base;
const getResponsiveFontSize = (base: number) => base;

const CustomerHomeScreen: React.FC = () => {
  const { colors } = useTheme();
  const { isAuthenticated, user } = useAuthStore();
  const { navigate } = useNavigationGuard();
  const { trackInteraction, trackAsyncOperation } = usePerformance({
    componentName: 'CustomerHomeScreen',
    trackRenders: true,
    trackInteractions: true,
  });

  // Track component lifecycle performance
  useLifecyclePerformance('CustomerHomeScreen');

  // Error handling
  const {
    error: globalError,
    isError: hasGlobalError,
    handleError,
    clearError,
    retry: retryGlobalError,
  } = useErrorHandling({
    maxRetries: 3,
    errorContext: { component: 'CustomerHomeScreen' },
  });

  const {
    data,
    loading,
    error,
    refreshing,
    refresh,
  } = useCustomerHomeData();

  const styles = useMemo(() => createStyles(colors), [colors]);

  // Get greeting based on time of day
  const getGreeting = useCallback(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }, []);

  const greeting = data.dashboard?.greeting || getGreeting();

  // Navigation handlers with performance tracking
  const handleCategoryPress = useCallback(async (category: ServiceCategory) => {
    await trackInteraction('category_press', async () => {
      console.log('Category pressed:', category.slug);
      await navigate('Search', { category: category.slug });
    });
  }, [navigate, trackInteraction]);

  const handleProviderPress = useCallback(async (provider: FeaturedProvider) => {
    await trackInteraction('provider_press', async () => {
      console.log('Provider pressed:', provider.id);
      await navigate('ProviderDetails', { providerId: provider.id });
    });
  }, [navigate, trackInteraction]);

  const handleSeeAllPress = useCallback(async (section: string) => {
    await trackInteraction('see_all_press', async () => {
      console.log('See all pressed for:', section);
      switch (section) {
        case 'featured':
          await navigate('Search', { filter: 'featured' });
          break;
        case 'favorites':
          await navigate('Search', { filter: 'favorites' });
          break;
        case 'nearby':
          await navigate('Search', { filter: 'nearby' });
          break;
        case 'quick-book':
          await navigate('Bookings', { tab: 'quick-book' });
          break;
        default:
          await navigate('Search');
      }
    });
  }, [navigate, trackInteraction]);

  // Announce screen content for screen readers
  useEffect(() => {
    const announceScreenContent = async () => {
      const isScreenReaderEnabled = await ScreenReaderUtils.isScreenReaderEnabled();
      if (isScreenReaderEnabled && data.categories && data.categories.length > 0) {
        // Delay announcement to allow screen to fully load
        setTimeout(() => {
          ScreenReaderUtils.announceForAccessibility(
            `Customer Home Screen loaded. ${greeting} ${user?.firstName || 'User'}. Browse ${data.categories.length} service categories including ${data.categories.map((c: ServiceCategory) => c.name).join(', ')}.`
          );
        }, 1000);
      }
    };

    announceScreenContent();
  }, [data.categories, greeting, user]);

  // Render functions
  const renderBrowseServicesSection = useCallback(() => {
    if (loading.categories && (!data.categories || data.categories.length === 0)) {
      return (
        <View style={[styles.section, styles.browseServicesSection]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Browse Services</Text>
          </View>
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        </View>
      );
    }

    if (error.categories && (!data.categories || data.categories.length === 0)) {
      return (
        <View style={[styles.section, styles.browseServicesSection]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Browse Services</Text>
          </View>
          <Text style={styles.errorText}>Failed to load categories. Pull to refresh.</Text>
        </View>
      );
    }

    return (
      <View style={[styles.section, styles.browseServicesSection]} accessibilityRole="none" accessibilityLabel="Browse Services section">
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Browse Services</Text>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScroll}
        >
          {(data.categories || []).map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[styles.categoryCard, { backgroundColor: category.color }]}
              onPress={() => handleCategoryPress(category)}
              accessibilityRole="button"
              accessibilityLabel={`${category.name} category, ${category.serviceCount} services`}
            >
              <Ionicons name={category.icon as any} size={28} color="#FFFFFF" />
              <Text style={styles.categoryName}>{category.name}</Text>
              <Text style={styles.categoryCount}>{category.serviceCount} services</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  }, [data.categories, loading.categories, error.categories, colors, handleCategoryPress]);

  const renderFeaturedProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Featured Providers section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Featured Providers</Heading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('featured')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all featured providers"
          />
        </View>
        {loading.featuredProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.featuredProviders ? (
          <Text style={styles.errorText}>Failed to load featured providers</Text>
        ) : data.featuredProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.featuredProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No featured providers available</Text>
        )}
      </View>
    );
  }, [data.featuredProviders, loading.featuredProviders, error.featuredProviders, colors, handleSeeAllPress]);

  const renderFavoriteProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Favorite Providers section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Favorite Providers</Heading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('favorites')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all favorite providers"
          />
        </View>
        {loading.favoriteProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.favoriteProviders ? (
          <Text style={styles.placeholderText}>Failed to load favorite providers</Text>
        ) : data.favoriteProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.favoriteProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No favorite providers yet</Text>
        )}
      </View>
    );
  }, [data.favoriteProviders, loading.favoriteProviders, error.favoriteProviders, colors, handleSeeAllPress]);

  const renderNearbyProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Nearby Providers section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Nearby Providers</Heading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('nearby')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all nearby providers"
          />
        </View>
        {loading.nearbyProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.nearbyProviders ? (
          <Text style={styles.placeholderText}>Failed to load nearby providers</Text>
        ) : data.nearbyProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.nearbyProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No nearby providers found</Text>
        )}
      </View>
    );
  }, [data.nearbyProviders, loading.nearbyProviders, error.nearbyProviders, colors, handleSeeAllPress]);

  const renderProviderCard = useCallback((provider: FeaturedProvider) => {
    return (
      <TouchableOpacity
        key={provider.id}
        style={styles.providerCard}
        onPress={() => handleProviderPress(provider)}
        accessibilityRole="button"
        accessibilityLabel={`${provider.name}, ${provider.rating} stars, ${provider.reviewCount} reviews`}
      >
        <View style={styles.providerImageContainer}>
          {provider.avatar ? (
            <Text style={styles.providerInitials}>
              {provider.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </Text>
          ) : (
            <Ionicons name="person-outline" size={24} color={colors.text.secondary} />
          )}
        </View>
        <View style={styles.providerInfo}>
          <Text style={styles.providerName} numberOfLines={1}>{provider.name}</Text>
          <View style={styles.providerRating}>
            <Ionicons name="star" size={12} color="#FFD700" />
            <Text style={styles.ratingText}>{provider.rating.toFixed(1)}</Text>
            <Text style={styles.reviewCount}>({provider.reviewCount})</Text>
          </View>
          <Text style={styles.providerLocation} numberOfLines={1}>
            {provider.location.city}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }, [colors, handleProviderPress]);

  const renderRecentBookingsSection = useCallback(() => {
    return (
      <View style={[styles.section, styles.recentBookingsSection]} accessibilityRole="none" accessibilityLabel="Recent Bookings and Quick Booking section">
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Bookings</Text>
          <FocusableButton
            title="Quick Book"
            onPress={() => handleSeeAllPress('quick-book')}
            variant="primary"
            size="small"
            accessibilityLabel="Quick booking"
          />
        </View>
        <View style={styles.recentBookingsContent}>
          <View style={styles.quickBookingCard}>
            <Ionicons name="add-circle-outline" size={32} color={colors?.sage400 || '#5A7A63'} />
            <Text style={styles.quickBookingTitle}>Quick Book a Service</Text>
            <Text style={styles.quickBookingSubtitle}>Book your favorite services with one tap</Text>
          </View>
          <Text style={styles.placeholderText}>Your recent bookings will appear here</Text>
        </View>
      </View>
    );
  }, [colors, handleSeeAllPress]);

  console.log('🏠 CustomerHomeScreen rendering... v2');
  console.log('🎨 Colors:', colors);
  console.log('📱 Categories:', data.categories.length);
  console.log('🔄 Loading:', loading.overall);

  // Show global error if there's a critical error
  if (hasGlobalError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <ErrorDisplay
          error={globalError?.message || 'An error occurred'}
          title="Unable to load home screen"
          description="We're having trouble loading your home screen. Your bookings and services are safe."
          actionLabel="Try Again"
          onAction={retryGlobalError}
          variant="fullscreen"
          severity="error"
          testID="customer-home-global-error"
        />
      </SafeAreaView>
    );
  }

  return (
    <EnhancedErrorBoundary
      screenName="Customer Home"
      enableRetry={true}
      maxRetries={3}
      enableReporting={true}
      onError={(error, errorInfo) => {
        handleError(new Error(error.message));
      }}
    >
      <SafeAreaView
        style={[styles.container, { backgroundColor: colors.background.primary }]}
        accessibilityRole="none"
        accessibilityLabel="Customer Home Screen"
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={refresh}
              colors={[colors.sage400 || '#5A7A63']}
              tintColor={colors.sage400 || '#5A7A63'}
              accessibilityLabel={refreshing ? "Refreshing content" : "Pull to refresh"}
            />
          }
          showsVerticalScrollIndicator={false}
          accessibilityRole="scrollbar"
          accessibilityLabel="Main content area"
          accessibilityHint="Scroll to browse services and providers"
        >
        {/* Hyper-Minimalist Header Section */}
        <View style={styles.headerSection} accessibilityRole="none" accessibilityLabel="Welcome header">
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>{greeting}</Text>
            <Text style={styles.userName}>
              {isAuthenticated && user?.firstName ? `${user.firstName}` : 'Welcome to Vierla'}
            </Text>
          </View>
        </View>

        {/* Browse Services Section - positioned right under header as per requirements */}
        {renderBrowseServicesSection()}

        {/* Featured Providers Section - first in provider hierarchy */}
        {renderFeaturedProvidersSection()}

        {/* Favorite Providers Section - second in provider hierarchy */}
        {isAuthenticated && renderFavoriteProvidersSection()}

        {/* Nearby Providers Section - third in provider hierarchy */}
        {renderNearbyProvidersSection()}

        {/* Recent Bookings merged with Quick Booking Section - final section */}
        {isAuthenticated && renderRecentBookingsSection()}


      </ScrollView>
    </SafeAreaView>
    </EnhancedErrorBoundary>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
    paddingHorizontal: 0, // Remove horizontal padding for full-width sections
  },
  // Hyper-Minimalist Header Section
  headerSection: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
    backgroundColor: colors?.sage400 || '#5A7A63',
    marginBottom: 0, // No margin for seamless flow
  },
  headerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  greeting: {
    fontSize: 16,
    color: colors?.text?.onPrimary || '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
  },
  userName: {
    fontSize: 28,
    fontWeight: '700',
    color: colors?.text?.onPrimary || '#FFFFFF',
    fontFamily: 'Inter-Bold',
    marginTop: 4,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  // Legacy welcome section (kept for compatibility)
  welcomeSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 12,
    marginBottom: 24,
  },
  section: {
    marginBottom: 32, // Increased spacing for minimalism
    paddingHorizontal: 20, // Consistent horizontal padding
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20, // Increased spacing
    paddingHorizontal: 0,
  },
  sectionTitle: {
    fontSize: 22, // Slightly larger for better hierarchy
    fontWeight: '600', // Reduced weight for minimalism
    color: colors?.text?.primary || '#333',
    fontFamily: 'Inter-SemiBold',
    letterSpacing: -0.3,
  },
  seeAllText: {
    fontSize: 14,
    color: colors?.sage400 || '#5A7A63',
    fontFamily: 'Inter-Medium',
    fontWeight: '500',
  },
  categoriesScroll: {
    paddingLeft: 0,
    paddingRight: 20, // Match section padding
  },
  // Browse Services Section - positioned right under header
  browseServicesSection: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    paddingTop: 24, // Extra top padding for separation from header
    marginBottom: 40, // Extra bottom margin for clear separation
  },
  categoryCard: {
    width: 130, // Slightly wider for better touch targets
    height: 110, // Slightly taller for better proportions
    borderRadius: 16, // More rounded for modern look
    padding: 16, // More padding for better spacing
    marginRight: 16, // Increased margin for better separation
    justifyContent: 'center',
    alignItems: 'center',
    // Subtle shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryName: {
    color: '#FFFFFF',
    fontSize: 15, // Slightly larger for better readability
    fontWeight: '600', // Reduced weight for minimalism
    marginTop: 10,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  categoryCount: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.85, // Slightly more opaque for better readability
    fontFamily: 'Inter-Regular',
  },
  placeholderText: {
    fontSize: 16,
    color: colors?.text?.tertiary || '#999',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: colors?.error || '#FF6B6B',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  providerCard: {
    width: 170, // Slightly wider for better content display
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 16, // More rounded for modern look
    padding: 16, // Increased padding for better spacing
    marginRight: 16, // Increased margin for better separation
    borderWidth: 0, // Remove border for cleaner look
    // Subtle shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 2,
  },
  providerImageContainer: {
    width: 56, // Larger for better visual hierarchy
    height: 56,
    borderRadius: 28,
    backgroundColor: colors?.sage100 || '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12, // Increased spacing
    alignSelf: 'center', // Center the avatar
  },
  providerInitials: {
    fontSize: 18, // Larger for better readability
    fontWeight: '600', // Reduced weight for minimalism
    color: colors?.sage600 || '#5A7A63',
    fontFamily: 'Inter-SemiBold',
  },
  providerInfo: {
    flex: 1,
    alignItems: 'center', // Center align for better symmetry
  },
  providerName: {
    fontSize: 15, // Slightly larger for better hierarchy
    fontWeight: '600', // Reduced weight for minimalism
    color: colors?.text?.primary || '#333',
    marginBottom: 6,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    justifyContent: 'center', // Center align
  },
  ratingText: {
    fontSize: 13, // Slightly larger for better readability
    color: colors?.text?.primary || '#333',
    marginLeft: 4,
    fontWeight: '500',
    fontFamily: 'Inter-Medium',
  },
  reviewCount: {
    fontSize: 12,
    color: colors?.text?.secondary || '#666',
    marginLeft: 2,
    fontFamily: 'Inter-Regular',
  },
  providerLocation: {
    fontSize: 12,
    color: colors?.text?.secondary || '#666',
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
  seeAllButton: {
    backgroundColor: 'transparent',
    minHeight: 32,
    paddingHorizontal: 8,
  },
  // Recent Bookings Section - merged with Quick Booking
  recentBookingsSection: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    marginBottom: 20, // Final section spacing
  },
  recentBookingsContent: {
    gap: 16, // Spacing between elements
  },
  quickBookingCard: {
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: colors?.sage100 || '#E8F5E8',
    borderStyle: 'dashed',
  },
  quickBookingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors?.text?.primary || '#333',
    marginTop: 12,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  quickBookingSubtitle: {
    fontSize: 14,
    color: colors?.text?.secondary || '#666',
    marginTop: 4,
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
});

export default CustomerHomeScreen;