import React, { use<PERSON><PERSON>back, useMemo, useEffect, useState } from 'react';
import {
  ScrollView,
  View,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
  SafeAreaView,
  Text,
  Dimensions,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

// Import enhanced components following Aura design system
import { FocusableButton } from '../components/accessibility/FocusableButton';
import { Heading } from '../components/typography/Typography';
import { ErrorDisplay } from '../components/error/ErrorDisplay';
import { EnhancedErrorBoundary } from '../components/error/EnhancedErrorBoundary';
import { EnhancedScreenReader, SemanticHeading } from '../components/accessibility/EnhancedScreenReader';
import { EnhancedTouchTarget } from '../components/accessibility/EnhancedTouchTarget';
import { accessibilityComplianceChecker } from '../utils/accessibilityComplianceChecker';

// Import hooks and services
import { useCustomerHomeData } from '../hooks/useCustomerHomeData';
import { useAuthStore } from '../store/authSlice';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigationGuard } from '../hooks/useNavigationGuard';
import { usePerformance, useLifecyclePerformance } from '../hooks/usePerformance';
import { useErrorHandling } from '../hooks/useErrorHandling';

// Import types and utilities
import { ScreenReaderUtils } from '../utils/accessibilityUtils';
import type { ServiceCategory, FeaturedProvider } from '../services/customerService';

// Import responsive utilities following Aura design system
import { getResponsiveSpacing, getResponsiveFontSize } from '../utils/responsiveUtils';

// Screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface CustomerHomeScreenProps {
  navigation: any;
  route?: any;
}

const CustomerHomeScreen: React.FC<CustomerHomeScreenProps> = ({ navigation }) => {
  const { colors } = useTheme();
  const { isAuthenticated, user } = useAuthStore();
  const { navigate } = useNavigationGuard();
  const { trackInteraction, trackAsyncOperation } = usePerformance({
    componentName: 'CustomerHomeScreen',
    trackRenders: true,
    trackInteractions: true,
  });

  // Enhanced state management for Aura design system
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showQuickActions, setShowQuickActions] = useState(true);

  // Track component lifecycle performance
  useLifecyclePerformance('CustomerHomeScreen');

  // Error handling
  const {
    error: globalError,
    isError: hasGlobalError,
    handleError,
    clearError,
    retry: retryGlobalError,
  } = useErrorHandling({
    maxRetries: 3,
    errorContext: { component: 'CustomerHomeScreen' },
  });

  const {
    data,
    loading,
    error,
    refreshing,
    refresh,
  } = useCustomerHomeData();

  const styles = useMemo(() => createStyles(colors), [colors]);

  // Enhanced greeting system following Aura design principles
  const getGreeting = useCallback(() => {
    const hour = new Date().getHours();
    const userName = user?.firstName || 'there';

    if (hour < 12) return `Good morning, ${userName}`;
    if (hour < 17) return `Good afternoon, ${userName}`;
    return `Good evening, ${userName}`;
  }, [user?.firstName]);

  const greeting = data.dashboard?.greeting || getGreeting();

  // Enhanced search functionality
  const handleSearchPress = useCallback(async () => {
    await trackInteraction('search_press', async () => {
      await navigate('Search', { query: searchQuery });
    });
  }, [navigate, trackInteraction, searchQuery]);

  // Quick action handlers following UX guidelines
  const handleQuickBooking = useCallback(async () => {
    await trackInteraction('quick_booking', async () => {
      if (!isAuthenticated) {
        await navigate('Login', { returnTo: 'QuickBooking' });
        return;
      }
      await navigate('QuickBooking');
    });
  }, [navigate, trackInteraction, isAuthenticated]);

  const handleEmergencyService = useCallback(async () => {
    await trackInteraction('emergency_service', async () => {
      await navigate('EmergencyServices');
    });
  }, [navigate, trackInteraction]);

  // Navigation handlers with performance tracking
  const handleCategoryPress = useCallback(async (category: ServiceCategory) => {
    await trackInteraction('category_press', async () => {
      console.log('Category pressed:', category.slug);
      await navigate('Search', { category: category.slug });
    });
  }, [navigate, trackInteraction]);

  const handleProviderPress = useCallback(async (provider: FeaturedProvider) => {
    await trackInteraction('provider_press', async () => {
      console.log('Provider pressed:', provider.id);
      await navigate('ProviderDetails', { providerId: provider.id });
    });
  }, [navigate, trackInteraction]);

  const handleSeeAllPress = useCallback(async (section: string) => {
    await trackInteraction('see_all_press', async () => {
      console.log('See all pressed for:', section);
      switch (section) {
        case 'featured':
          await navigate('Search', { filter: 'featured' });
          break;
        case 'favorites':
          await navigate('Search', { filter: 'favorites' });
          break;
        case 'nearby':
          await navigate('Search', { filter: 'nearby' });
          break;
        case 'quick-book':
          await navigate('Bookings', { tab: 'quick-book' });
          break;
        default:
          await navigate('Search');
      }
    });
  }, [navigate, trackInteraction]);

  // Announce screen content for screen readers
  useEffect(() => {
    const announceScreenContent = async () => {
      const isScreenReaderEnabled = await ScreenReaderUtils.isScreenReaderEnabled();
      if (isScreenReaderEnabled && data.categories && data.categories.length > 0) {
        // Delay announcement to allow screen to fully load
        setTimeout(() => {
          ScreenReaderUtils.announceForAccessibility(
            `Customer Home Screen loaded. ${greeting} ${user?.firstName || 'User'}. Browse ${data.categories.length} service categories including ${data.categories.map((c: ServiceCategory) => c.name).join(', ')}.`
          );
        }, 1000);
      }
    };

    announceScreenContent();
  }, [data.categories, greeting, user]);

  // Enhanced header with Aura design system
  const renderEnhancedHeader = () => (
    <View style={styles.enhancedHeader}>
      <LinearGradient
        colors={[colors.primary.default, colors.primary.light]}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <SafeAreaView style={styles.headerSafeArea}>
          <View style={styles.headerContent}>
            {/* Greeting Section */}
            <View style={styles.greetingSection}>
              <Text style={styles.enhancedGreeting}>{greeting}</Text>
              <Text style={styles.subGreeting}>
                What service do you need today?
              </Text>
            </View>

            {/* Header Actions */}
            <View style={styles.headerActions}>
              <EnhancedTouchTarget
                style={styles.headerActionButton}
                onPress={() => navigation.navigate('Notifications')}
                accessibilityLabel="View notifications"
                accessibilityHint="Opens your notifications"
                testID="notifications-button"
                minimumSize={44}
                showTouchFeedback={true}
              >
                <View style={styles.notificationContainer}>
                  <Ionicons
                    name="notifications-outline"
                    size={24}
                    color={colors.surface.primary}
                  />
                  {data.unreadNotifications > 0 && (
                    <View style={styles.notificationBadge}>
                      <Text style={styles.badgeText}>
                        {data.unreadNotifications > 9 ? '9+' : data.unreadNotifications}
                      </Text>
                    </View>
                  )}
                </View>
              </EnhancedTouchTarget>

              <EnhancedTouchTarget
                style={styles.headerActionButton}
                onPress={() => navigation.navigate('Profile')}
                accessibilityLabel="View profile"
                accessibilityHint="Opens your profile settings"
                testID="profile-button"
                minimumSize={44}
                showTouchFeedback={true}
              >
                {user?.avatar ? (
                  <Image
                    source={{ uri: user.avatar }}
                    style={styles.profileAvatar}
                  />
                ) : (
                  <View style={styles.profilePlaceholder}>
                    <Ionicons
                      name="person"
                      size={20}
                      color={colors.surface.primary}
                    />
                  </View>
                )}
              </EnhancedTouchTarget>
            </View>
          </View>

          {/* Enhanced Search Bar */}
          <View style={styles.searchContainer}>
            <EnhancedTouchTarget
              style={styles.searchBar}
              onPress={handleSearchPress}
              accessibilityLabel="Search for services"
              accessibilityHint="Tap to search for services and providers"
              testID="search-bar"
              minimumSize={48}
              showTouchFeedback={true}
            >
              <Ionicons
                name="search"
                size={20}
                color={colors.text.tertiary}
              />
              <Text style={styles.searchPlaceholder}>
                Search services, providers...
              </Text>
              <Ionicons
                name="mic-outline"
                size={20}
                color={colors.text.tertiary}
              />
            </EnhancedTouchTarget>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );

  // Quick Actions Component following Aura design
  const renderQuickActions = () => {
    if (!showQuickActions) return null;

    return (
      <View style={styles.quickActionsContainer}>
        <View style={styles.quickActionsHeader}>
          <Text style={styles.quickActionsTitle}>Quick Actions</Text>
          <EnhancedTouchTarget
            onPress={() => setShowQuickActions(false)}
            accessibilityLabel="Hide quick actions"
            minimumSize={32}
          >
            <Ionicons name="close" size={20} color={colors.text.secondary} />
          </EnhancedTouchTarget>
        </View>

        <View style={styles.quickActionsGrid}>
          <EnhancedTouchTarget
            style={styles.quickActionCard}
            onPress={handleQuickBooking}
            accessibilityLabel="Quick booking"
            accessibilityHint="Book a service quickly"
            testID="quick-booking-button"
            minimumSize={80}
            showTouchFeedback={true}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.success.light }]}>
              <Ionicons name="flash" size={24} color={colors.success.default} />
            </View>
            <Text style={styles.quickActionText}>Quick Book</Text>
          </EnhancedTouchTarget>

          <EnhancedTouchTarget
            style={styles.quickActionCard}
            onPress={handleEmergencyService}
            accessibilityLabel="Emergency service"
            accessibilityHint="Access emergency services"
            testID="emergency-service-button"
            minimumSize={80}
            showTouchFeedback={true}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.error.light }]}>
              <Ionicons name="medical" size={24} color={colors.error.default} />
            </View>
            <Text style={styles.quickActionText}>Emergency</Text>
          </EnhancedTouchTarget>

          <EnhancedTouchTarget
            style={styles.quickActionCard}
            onPress={() => navigation.navigate('Favorites')}
            accessibilityLabel="View favorites"
            accessibilityHint="View your favorite providers"
            testID="favorites-button"
            minimumSize={80}
            showTouchFeedback={true}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.warning.light }]}>
              <Ionicons name="heart" size={24} color={colors.warning.default} />
            </View>
            <Text style={styles.quickActionText}>Favorites</Text>
          </EnhancedTouchTarget>

          <EnhancedTouchTarget
            style={styles.quickActionCard}
            onPress={() => navigation.navigate('History')}
            accessibilityLabel="View history"
            accessibilityHint="View your booking history"
            testID="history-button"
            minimumSize={80}
            showTouchFeedback={true}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.info.light }]}>
              <Ionicons name="time" size={24} color={colors.info.default} />
            </View>
            <Text style={styles.quickActionText}>History</Text>
          </EnhancedTouchTarget>
        </View>
      </View>
    );
  };

  // Enhanced Browse Services Section with Aura Design System
  const renderEnhancedBrowseServicesSection = useCallback(() => {
    if (loading.categories && (!data.categories || data.categories.length === 0)) {
      return (
        <View style={styles.enhancedBrowseSection}>
          <View style={styles.enhancedSectionHeader}>
            <Text style={styles.enhancedSectionTitle}>Browse Services</Text>
            <Text style={styles.sectionSubtitle}>Find the perfect service for your needs</Text>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary?.default} />
            <Text style={styles.loadingText}>Loading services...</Text>
          </View>
        </View>
      );
    }

    if (error.categories && (!data.categories || data.categories.length === 0)) {
      return (
        <View style={styles.enhancedBrowseSection}>
          <View style={styles.enhancedSectionHeader}>
            <Text style={styles.enhancedSectionTitle}>Browse Services</Text>
          </View>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={48} color={colors.error?.default} />
            <Text style={styles.errorTitle}>Unable to load services</Text>
            <Text style={styles.errorSubtitle}>Pull down to refresh and try again</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.enhancedBrowseSection}>
        <View style={styles.enhancedSectionHeader}>
          <Text style={styles.enhancedSectionTitle}>Browse Services</Text>
          <Text style={styles.sectionSubtitle}>
            {data.categories?.length || 0} categories available
          </Text>
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.enhancedCategoriesScroll}
          accessibilityRole="list"
          accessibilityLabel="Service categories"
        >
          {(data.categories || []).map((category, index) => (
            <EnhancedTouchTarget
              key={category.id}
              style={styles.enhancedCategoryCard}
              onPress={() => handleCategoryPress(category)}
              accessibilityLabel={`${category.name} category`}
              accessibilityHint="Tap to browse services in this category"
              testID={`category-card-${category.id}`}
              minimumSize={120}
              showTouchFeedback={true}
              enableVisualFeedback={true}
            >
              <LinearGradient
                colors={category.gradient || [category.color, category.color]}
                style={styles.categoryGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={styles.categoryIconContainer}>
                  <Ionicons
                    name={category.icon as any}
                    size={32}
                    color="#FFFFFF"
                    accessibilityLabel={`${category.name} icon`}
                  />
                </View>

                <View style={styles.categoryInfo}>
                  <Text style={styles.enhancedCategoryName} numberOfLines={2}>
                    {category.name}
                  </Text>

                  {category.serviceCount && (
                    <Text style={styles.categoryServiceCount}>
                      {category.serviceCount} services
                    </Text>
                  )}

                  {category.isPopular && (
                    <View style={styles.popularBadge}>
                      <Ionicons name="trending-up" size={12} color="#FFFFFF" />
                      <Text style={styles.popularText}>Popular</Text>
                    </View>
                  )}
                </View>
              </LinearGradient>
            </EnhancedTouchTarget>
          ))}

          {/* View All Categories Card */}
          <EnhancedTouchTarget
            style={styles.viewAllCategoriesCard}
            onPress={() => navigation.navigate('Categories')}
            accessibilityLabel="View all categories"
            accessibilityHint="Tap to see all available service categories"
            testID="view-all-categories"
            minimumSize={120}
            showTouchFeedback={true}
          >
            <View style={styles.viewAllContent}>
              <View style={styles.viewAllIconContainer}>
                <Ionicons name="grid" size={24} color={colors.primary?.default} />
              </View>
              <Text style={styles.viewAllText}>View All</Text>
              <Text style={styles.viewAllSubtext}>
                {data.categories?.length || 0}+ categories
              </Text>
            </View>
          </EnhancedTouchTarget>
        </ScrollView>
      </View>
    );
  }, [data.categories, loading.categories, error.categories, colors, handleCategoryPress, navigation]);

  const renderFeaturedProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Featured Providers section">
        <View style={styles.sectionHeader}>
          <SemanticHeading
            level={2}
            style={{ color: colors.text.primary, fontSize: 20, fontWeight: 'bold' }}
            accessibilityLabel="Featured Providers section heading"
          >
            Featured Providers
          </SemanticHeading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('featured')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all featured providers"
          />
        </View>
        {loading.featuredProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.featuredProviders ? (
          <Text style={styles.errorText}>Failed to load featured providers</Text>
        ) : data.featuredProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.featuredProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderEnhancedProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No featured providers available</Text>
        )}
      </View>
    );
  }, [data.featuredProviders, loading.featuredProviders, error.featuredProviders, colors, handleSeeAllPress]);

  const renderFavoriteProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Favorite Providers section">
        <View style={styles.sectionHeader}>
          <SemanticHeading
            level={2}
            style={{ color: colors.text.primary, fontSize: 20, fontWeight: 'bold' }}
            accessibilityLabel="Favorite Providers section heading"
          >
            Favorite Providers
          </SemanticHeading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('favorites')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all favorite providers"
          />
        </View>
        {loading.favoriteProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.favoriteProviders ? (
          <Text style={styles.placeholderText}>Failed to load favorite providers</Text>
        ) : data.favoriteProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.favoriteProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderEnhancedProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No favorite providers yet</Text>
        )}
      </View>
    );
  }, [data.favoriteProviders, loading.favoriteProviders, error.favoriteProviders, colors, handleSeeAllPress]);

  const renderNearbyProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Nearby Providers section">
        <View style={styles.sectionHeader}>
          <SemanticHeading
            level={2}
            style={{ color: colors.text.primary, fontSize: 20, fontWeight: 'bold' }}
            accessibilityLabel="Nearby Providers section heading"
          >
            Nearby Providers
          </SemanticHeading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('nearby')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all nearby providers"
          />
        </View>
        {loading.nearbyProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.nearbyProviders ? (
          <Text style={styles.placeholderText}>Failed to load nearby providers</Text>
        ) : data.nearbyProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.nearbyProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderEnhancedProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No nearby providers found</Text>
        )}
      </View>
    );
  }, [data.nearbyProviders, loading.nearbyProviders, error.nearbyProviders, colors, handleSeeAllPress]);

  // Enhanced Provider Card with Aura Design System
  const renderEnhancedProviderCard = useCallback((provider: FeaturedProvider) => {
    return (
      <EnhancedTouchTarget
        key={provider.id}
        style={styles.enhancedProviderCard}
        onPress={() => handleProviderPress(provider)}
        accessibilityLabel={`${provider.name}, ${provider.rating} stars, ${provider.reviewCount} reviews`}
        accessibilityHint="Tap to view provider details and book services"
        testID={`provider-card-${provider.id}`}
        minimumSize={120}
        showTouchFeedback={true}
        enableVisualFeedback={true}
      >
        <View style={styles.providerCardContent}>
          {/* Provider Avatar/Image */}
          <View style={styles.enhancedProviderImageContainer}>
            {provider.avatar ? (
              <Image
                source={{ uri: provider.avatar }}
                style={styles.providerAvatar}
                accessibilityLabel={`${provider.name} profile picture`}
              />
            ) : (
              <View style={styles.providerAvatarPlaceholder}>
                <Text style={styles.providerInitials}>
                  {provider.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </Text>
              </View>
            )}

            {/* Online Status Indicator */}
            {provider.isOnline && (
              <View style={styles.onlineIndicator}>
                <View style={styles.onlineDot} />
              </View>
            )}
          </View>

          {/* Provider Information */}
          <View style={styles.enhancedProviderInfo}>
            <Text style={styles.enhancedProviderName} numberOfLines={1}>
              {provider.name}
            </Text>

            {/* Rating and Reviews */}
            <View style={styles.enhancedProviderRating}>
              <View style={styles.ratingStars}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Ionicons
                    key={star}
                    name={star <= Math.floor(provider.rating) ? "star" : "star-outline"}
                    size={12}
                    color="#FFD700"
                  />
                ))}
              </View>
              <Text style={styles.enhancedRatingText}>
                {provider.rating.toFixed(1)} ({provider.reviewCount})
              </Text>
            </View>

            {/* Services Preview */}
            {provider.services && provider.services.length > 0 && (
              <Text style={styles.providerServices} numberOfLines={1}>
                {provider.services.slice(0, 2).join(', ')}
                {provider.services.length > 2 && '...'}
              </Text>
            )}

            {/* Location and Distance */}
            <View style={styles.providerLocationContainer}>
              <Ionicons name="location-outline" size={12} color={colors.text.tertiary} />
              <Text style={styles.enhancedProviderLocation} numberOfLines={1}>
                {provider.location?.city || 'Location not available'}
              </Text>
              {provider.distance && (
                <Text style={styles.providerDistance}>
                  • {provider.distance}km
                </Text>
              )}
            </View>

            {/* Quick Action Buttons */}
            <View style={styles.providerActions}>
              <EnhancedTouchTarget
                style={styles.quickCallButton}
                onPress={() => handleQuickCall(provider)}
                accessibilityLabel={`Call ${provider.name}`}
                minimumSize={32}
                showTouchFeedback={true}
              >
                <Ionicons name="call" size={16} color={colors.primary.default} />
              </EnhancedTouchTarget>

              <EnhancedTouchTarget
                style={styles.quickMessageButton}
                onPress={() => handleQuickMessage(provider)}
                accessibilityLabel={`Message ${provider.name}`}
                minimumSize={32}
                showTouchFeedback={true}
              >
                <Ionicons name="chatbubble" size={16} color={colors.primary.default} />
              </EnhancedTouchTarget>

              <EnhancedTouchTarget
                style={styles.favoriteButton}
                onPress={() => handleToggleFavorite(provider)}
                accessibilityLabel={provider.isFavorite ? `Remove ${provider.name} from favorites` : `Add ${provider.name} to favorites`}
                minimumSize={32}
                showTouchFeedback={true}
              >
                <Ionicons
                  name={provider.isFavorite ? "heart" : "heart-outline"}
                  size={16}
                  color={provider.isFavorite ? colors.error.default : colors.text.tertiary}
                />
              </EnhancedTouchTarget>
            </View>
          </View>
        </View>
      </EnhancedTouchTarget>
    );
  }, [colors, handleProviderPress]);

  // Quick action handlers for provider cards
  const handleQuickCall = useCallback(async (provider: FeaturedProvider) => {
    await trackInteraction('provider_quick_call', async () => {
      // Implement quick call functionality
      navigation.navigate('Call', { providerId: provider.id });
    });
  }, [navigation, trackInteraction]);

  const handleQuickMessage = useCallback(async (provider: FeaturedProvider) => {
    await trackInteraction('provider_quick_message', async () => {
      navigation.navigate('Chat', { providerId: provider.id });
    });
  }, [navigation, trackInteraction]);

  const handleToggleFavorite = useCallback(async (provider: FeaturedProvider) => {
    await trackInteraction('provider_toggle_favorite', async () => {
      // Implement favorite toggle functionality
      // This would typically call an API to update the favorite status
    });
  }, [trackInteraction]);

  const renderRecentBookingsSection = useCallback(() => {
    return (
      <View style={[styles.section, styles.recentBookingsSection]} accessibilityRole="none" accessibilityLabel="Recent Bookings and Quick Booking section">
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Bookings</Text>
          <FocusableButton
            title="Quick Book"
            onPress={() => handleSeeAllPress('quick-book')}
            variant="primary"
            size="small"
            accessibilityLabel="Quick booking"
          />
        </View>
        <View style={styles.recentBookingsContent}>
          <View style={styles.quickBookingCard}>
            <Ionicons name="add-circle-outline" size={32} color={colors?.sage400 || '#5A7A63'} />
            <Text style={styles.quickBookingTitle}>Quick Book a Service</Text>
            <Text style={styles.quickBookingSubtitle}>Book your favorite services with one tap</Text>
          </View>
          <Text style={styles.placeholderText}>Your recent bookings will appear here</Text>
        </View>
      </View>
    );
  }, [colors, handleSeeAllPress]);

  console.log('🏠 CustomerHomeScreen rendering... v2');
  console.log('🎨 Colors:', colors);
  console.log('📱 Categories:', data.categories.length);
  console.log('🔄 Loading:', loading.overall);

  // Show global error if there's a critical error
  if (hasGlobalError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <ErrorDisplay
          error={globalError?.message || 'An error occurred'}
          title="Unable to load home screen"
          description="We're having trouble loading your home screen. Your bookings and services are safe."
          actionLabel="Try Again"
          onAction={retryGlobalError}
          variant="fullscreen"
          severity="error"
          testID="customer-home-global-error"
        />
      </SafeAreaView>
    );
  }

  return (
    <EnhancedErrorBoundary
      screenName="Customer Home"
      enableRetry={true}
      maxRetries={3}
      enableReporting={true}
      onError={(error, errorInfo) => {
        handleError(new Error(error.message));
      }}
    >
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <EnhancedScreenReader
          region={{
            id: 'customer-home-main',
            label: 'Customer Home Screen',
            role: 'main',
            description: 'Main content area showing services, providers, and bookings',
            live: 'polite'
          }}
          announceOnMount="Welcome to Vierla. Browse services, view featured providers, and manage your bookings."
          skipToContent={true}
          landmarkNavigation={true}
          autoDescribe={true}
        >
          {/* Enhanced Header with Aura Design System */}
          {renderEnhancedHeader()}

          {/* Quick Actions Section */}
          {renderQuickActions()}

          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={refresh}
                colors={[colors.primary?.default || '#5A7A63']}
                tintColor={colors.primary?.default || '#5A7A63'}
                accessibilityLabel={refreshing ? "Refreshing content" : "Pull to refresh"}
              />
            }
            showsVerticalScrollIndicator={false}
            accessibilityRole="scrollbar"
            accessibilityLabel="Main content area"
            accessibilityHint="Scroll to browse services and providers"
            testID="customer-home-scroll"
          >

        {/* Enhanced Browse Services Section - positioned right under header as per requirements */}
        {renderEnhancedBrowseServicesSection()}

        {/* Featured Providers Section - first in provider hierarchy */}
        {renderFeaturedProvidersSection()}

        {/* Favorite Providers Section - second in provider hierarchy */}
        {isAuthenticated && renderFavoriteProvidersSection()}

        {/* Nearby Providers Section - third in provider hierarchy */}
        {renderNearbyProvidersSection()}

        {/* Recent Bookings merged with Quick Booking Section - final section */}
        {isAuthenticated && renderRecentBookingsSection()}


          </ScrollView>
        </EnhancedScreenReader>
      </View>
    </EnhancedErrorBoundary>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
    paddingHorizontal: 0, // Remove horizontal padding for full-width sections
  },

  // Enhanced Header Styles following Aura design system
  enhancedHeader: {
    position: 'relative',
    zIndex: 10,
  },
  headerGradient: {
    paddingBottom: getResponsiveSpacing(4),
  },
  headerSafeArea: {
    paddingTop: getResponsiveSpacing(2),
  },
  greetingSection: {
    flex: 1,
    marginRight: getResponsiveSpacing(3),
  },
  enhancedGreeting: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: colors?.surface?.primary || '#FFFFFF',
    marginBottom: getResponsiveSpacing(1),
  },
  subGreeting: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.surface?.secondary || 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(2),
  },
  headerActionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: colors?.error?.default || '#FF6B6B',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  profilePlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: getResponsiveSpacing(4),
    paddingTop: getResponsiveSpacing(3),
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors?.surface?.primary || '#FFFFFF',
    borderRadius: 25,
    paddingHorizontal: getResponsiveSpacing(4),
    paddingVertical: getResponsiveSpacing(3),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    gap: getResponsiveSpacing(3),
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: getResponsiveFontSize(16),
    color: colors?.text?.tertiary || '#9E9E9E',
  },
  quickActionsContainer: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    marginHorizontal: getResponsiveSpacing(4),
    marginTop: getResponsiveSpacing(4),
    borderRadius: 16,
    padding: getResponsiveSpacing(4),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  quickActionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(3),
  },
  quickActionsTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: getResponsiveSpacing(2),
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(3),
    borderRadius: 12,
    backgroundColor: colors?.background?.secondary || '#F5F5F5',
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  quickActionText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors?.text?.secondary || '#666666',
    textAlign: 'center',
  },

  // Enhanced Provider Card Styles
  enhancedProviderCard: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    borderRadius: 16,
    marginRight: getResponsiveSpacing(3),
    marginBottom: getResponsiveSpacing(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    width: 280,
    overflow: 'hidden',
  },
  providerCardContent: {
    padding: getResponsiveSpacing(4),
  },
  enhancedProviderImageContainer: {
    position: 'relative',
    alignSelf: 'flex-start',
    marginBottom: getResponsiveSpacing(3),
  },
  providerAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  providerAvatarPlaceholder: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors?.primary?.light || '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  providerInitials: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors?.primary?.default || '#4CAF50',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    borderRadius: 10,
    padding: 2,
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors?.success?.default || '#4CAF50',
  },
  enhancedProviderInfo: {
    flex: 1,
  },
  enhancedProviderName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
    marginBottom: getResponsiveSpacing(1),
  },
  enhancedProviderRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  ratingStars: {
    flexDirection: 'row',
    marginRight: getResponsiveSpacing(1),
  },
  enhancedRatingText: {
    fontSize: getResponsiveFontSize(12),
    color: colors?.text?.secondary || '#666666',
    fontWeight: '500',
  },
  providerServices: {
    fontSize: getResponsiveFontSize(12),
    color: colors?.text?.tertiary || '#9E9E9E',
    marginBottom: getResponsiveSpacing(2),
  },
  providerLocationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(3),
  },
  enhancedProviderLocation: {
    fontSize: getResponsiveFontSize(12),
    color: colors?.text?.tertiary || '#9E9E9E',
    marginLeft: 4,
    flex: 1,
  },
  providerDistance: {
    fontSize: getResponsiveFontSize(12),
    color: colors?.text?.tertiary || '#9E9E9E',
    marginLeft: 4,
  },
  providerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quickCallButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors?.primary?.light || '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickMessageButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors?.info?.light || '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors?.background?.secondary || '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Enhanced Browse Services Section Styles
  enhancedBrowseSection: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    marginTop: getResponsiveSpacing(2),
    paddingVertical: getResponsiveSpacing(4),
  },
  enhancedSectionHeader: {
    paddingHorizontal: getResponsiveSpacing(4),
    marginBottom: getResponsiveSpacing(3),
  },
  enhancedSectionTitle: {
    fontSize: getResponsiveFontSize(22),
    fontWeight: '700',
    color: colors?.text?.primary || '#000000',
    marginBottom: getResponsiveSpacing(1),
  },
  sectionSubtitle: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
    fontWeight: '400',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(6),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
    marginTop: getResponsiveSpacing(2),
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(6),
    paddingHorizontal: getResponsiveSpacing(4),
  },
  errorTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
    marginTop: getResponsiveSpacing(2),
    marginBottom: getResponsiveSpacing(1),
  },
  errorSubtitle: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
    textAlign: 'center',
  },
  enhancedCategoriesScroll: {
    paddingHorizontal: getResponsiveSpacing(4),
    paddingRight: getResponsiveSpacing(6),
  },
  enhancedCategoryCard: {
    width: 140,
    height: 120,
    marginRight: getResponsiveSpacing(3),
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  categoryGradient: {
    flex: 1,
    padding: getResponsiveSpacing(3),
    justifyContent: 'space-between',
  },
  categoryIconContainer: {
    alignSelf: 'flex-start',
  },
  categoryInfo: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  enhancedCategoryName: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: getResponsiveSpacing(1),
  },
  categoryServiceCount: {
    fontSize: getResponsiveFontSize(12),
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
  },
  popularBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    alignSelf: 'flex-start',
    marginTop: getResponsiveSpacing(1),
  },
  popularText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 2,
  },
  viewAllCategoriesCard: {
    width: 100,
    height: 120,
    backgroundColor: colors?.background?.secondary || '#F5F5F5',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: colors?.border?.primary || '#E0E0E0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewAllContent: {
    alignItems: 'center',
  },
  viewAllIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors?.primary?.light || '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  viewAllText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
    marginBottom: 2,
  },
  viewAllSubtext: {
    fontSize: getResponsiveFontSize(10),
    color: colors?.text?.tertiary || '#9E9E9E',
    textAlign: 'center',
  },

  // Hyper-Minimalist Header Section
  headerSection: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
    backgroundColor: colors?.sage400 || '#5A7A63',
    marginBottom: 0, // No margin for seamless flow
  },
  headerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  greeting: {
    fontSize: 16,
    color: colors?.text?.onPrimary || '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
  },
  userName: {
    fontSize: 28,
    fontWeight: '700',
    color: colors?.text?.onPrimary || '#FFFFFF',
    fontFamily: 'Inter-Bold',
    marginTop: 4,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  // Legacy welcome section (kept for compatibility)
  welcomeSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 12,
    marginBottom: 24,
  },
  section: {
    marginBottom: 32, // Increased spacing for minimalism
    paddingHorizontal: 20, // Consistent horizontal padding
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20, // Increased spacing
    paddingHorizontal: 0,
  },
  sectionTitle: {
    fontSize: 22, // Slightly larger for better hierarchy
    fontWeight: '600', // Reduced weight for minimalism
    color: colors?.text?.primary || '#333',
    fontFamily: 'Inter-SemiBold',
    letterSpacing: -0.3,
  },
  seeAllText: {
    fontSize: 14,
    color: colors?.sage400 || '#5A7A63',
    fontFamily: 'Inter-Medium',
    fontWeight: '500',
  },
  categoriesScroll: {
    paddingLeft: 0,
    paddingRight: 20, // Match section padding
  },
  // Browse Services Section - positioned right under header
  browseServicesSection: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    paddingTop: 24, // Extra top padding for separation from header
    marginBottom: 40, // Extra bottom margin for clear separation
  },
  categoryCard: {
    width: 130, // Slightly wider for better touch targets
    height: 110, // Slightly taller for better proportions
    borderRadius: 16, // More rounded for modern look
    padding: 16, // More padding for better spacing
    marginRight: 16, // Increased margin for better separation
    justifyContent: 'center',
    alignItems: 'center',
    // Subtle shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryName: {
    color: '#FFFFFF',
    fontSize: 15, // Slightly larger for better readability
    fontWeight: '600', // Reduced weight for minimalism
    marginTop: 10,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  categoryCount: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.85, // Slightly more opaque for better readability
    fontFamily: 'Inter-Regular',
  },
  placeholderText: {
    fontSize: 16,
    color: colors?.text?.tertiary || '#999',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: colors?.error || '#FF6B6B',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  providerCard: {
    width: 170, // Slightly wider for better content display
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 16, // More rounded for modern look
    padding: 16, // Increased padding for better spacing
    marginRight: 16, // Increased margin for better separation
    borderWidth: 0, // Remove border for cleaner look
    // Subtle shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 2,
  },
  providerImageContainer: {
    width: 56, // Larger for better visual hierarchy
    height: 56,
    borderRadius: 28,
    backgroundColor: colors?.sage100 || '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12, // Increased spacing
    alignSelf: 'center', // Center the avatar
  },
  providerInfo: {
    flex: 1,
    alignItems: 'center', // Center align for better symmetry
  },
  providerName: {
    fontSize: 15, // Slightly larger for better hierarchy
    fontWeight: '600', // Reduced weight for minimalism
    color: colors?.text?.primary || '#333',
    marginBottom: 6,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    justifyContent: 'center', // Center align
  },
  ratingText: {
    fontSize: 13, // Slightly larger for better readability
    color: colors?.text?.primary || '#333',
    marginLeft: 4,
    fontWeight: '500',
    fontFamily: 'Inter-Medium',
  },
  reviewCount: {
    fontSize: 12,
    color: colors?.text?.secondary || '#666',
    marginLeft: 2,
    fontFamily: 'Inter-Regular',
  },
  providerLocation: {
    fontSize: 12,
    color: colors?.text?.secondary || '#666',
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
  seeAllButton: {
    backgroundColor: 'transparent',
    minHeight: 32,
    paddingHorizontal: 8,
  },
  // Recent Bookings Section - merged with Quick Booking
  recentBookingsSection: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    marginBottom: 20, // Final section spacing
  },
  recentBookingsContent: {
    gap: 16, // Spacing between elements
  },
  quickBookingCard: {
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: colors?.sage100 || '#E8F5E8',
    borderStyle: 'dashed',
  },
  quickBookingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors?.text?.primary || '#333',
    marginTop: 12,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  quickBookingSubtitle: {
    fontSize: 14,
    color: colors?.text?.secondary || '#666',
    marginTop: 4,
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
});

export default CustomerHomeScreen;