/**
 * Enhanced Error Boundary Component
 * Provides comprehensive error handling with recovery mechanisms
 */

import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { errorHandler } from '../../utils/errorHandler';
import { performanceMonitor } from '../../services/performanceMonitor';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  screenName?: string;
  criticalError?: boolean;
  showErrorDetails?: boolean;
  enableReporting?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  retryCount: number;
  errorId: string | null;
  lastErrorTime: number;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: null,
      lastErrorTime: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      lastErrorTime: Date.now(),
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.setState({
      errorInfo,
      errorId,
    });

    // Handle error with our error handler
    const handledError = errorHandler.handleError(error, {
      component: this.props.screenName || 'EnhancedErrorBoundary',
      errorInfo,
      errorId,
      retryCount: this.state.retryCount,
      criticalError: this.props.criticalError,
    });

    // Track error in performance monitoring
    performanceMonitor.trackError(error, {
      component: this.props.screenName || 'EnhancedErrorBoundary',
      errorId,
      retryCount: this.state.retryCount,
      criticalError: this.props.criticalError,
    });

    // Report error if enabled
    if (this.props.enableReporting !== false) {
      this.reportError(error, errorInfo, errorId);
    }

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  componentDidUpdate(prevProps: Props) {
    const { resetKeys, resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys?.some((key, idx) => prevProps.resetKeys?.[idx] !== key)) {
        this.resetErrorBoundary();
      }
    }

    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetErrorBoundary();
    }
  }

  reportError = async (error: Error, errorInfo: any, errorId: string) => {
    try {
      // Here you would integrate with your error reporting service
      // For now, we'll just log it comprehensively
      console.error('🚨 Error Boundary Report:', {
        errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        screenName: this.props.screenName,
        retryCount: this.state.retryCount,
        timestamp: new Date().toISOString(),
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount } = this.state;

    if (retryCount < maxRetries) {
      this.setState(
        prevState => ({
          retryCount: prevState.retryCount + 1,
        }),
        () => {
          // Reset after a short delay to allow for cleanup
          this.resetTimeoutId = setTimeout(() => {
            this.resetErrorBoundary();
          }, 100) as any;
        }
      );

      // Track retry attempt
      performanceMonitor.trackUserInteraction('error_boundary_retry', 0, {
        retryCount: retryCount + 1,
        errorId: this.state.errorId,
        screenName: this.props.screenName,
      });
    }
  };

  handleReportIssue = () => {
    const { error, errorId } = this.state;
    
    Alert.alert(
      'Report Issue',
      'Would you like to report this issue to help us improve the app?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Report',
          onPress: () => {
            // Here you would integrate with your issue reporting system
            console.log('User reported issue:', { errorId, error: error?.message });
            Alert.alert('Thank you', 'Your report has been submitted.');
          },
        },
      ]
    );
  };

  render() {
    const { hasError, error, retryCount } = this.state;
    const { 
      children, 
      fallback, 
      enableRetry = true, 
      maxRetries = 3,
      screenName,
      criticalError = false,
      showErrorDetails = false,
    } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      const errorTitle = criticalError 
        ? `${screenName || 'Application'} Error` 
        : 'Something went wrong';
      
      const errorMessage = criticalError
        ? `There's a critical issue with ${screenName?.toLowerCase() || 'the application'}. Please try again or restart the app.`
        : error?.message || 'An unexpected error occurred. Please try again.';

      return (
        <View style={[styles.container, criticalError && styles.criticalContainer]}>
          <View style={styles.content}>
            <Ionicons 
              name={criticalError ? "warning-outline" : "alert-circle-outline"} 
              size={48} 
              color={criticalError ? "#FF6B6B" : "#FFA726"} 
            />
            <Text style={[styles.title, criticalError && styles.criticalTitle]}>
              {errorTitle}
            </Text>
            <Text style={styles.message}>
              {errorMessage}
            </Text>
            
            {showErrorDetails && error && (
              <Text style={styles.errorDetails}>
                {error.stack?.split('\n')[0] || error.message}
              </Text>
            )}
            
            <View style={styles.buttonContainer}>
              {enableRetry && retryCount < maxRetries && (
                <TouchableOpacity 
                  style={[styles.retryButton, criticalError && styles.criticalButton]} 
                  onPress={this.handleRetry}
                >
                  <Text style={styles.retryButtonText}>
                    Try Again {retryCount > 0 && `(${retryCount}/${maxRetries})`}
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity 
                style={styles.reportButton} 
                onPress={this.handleReportIssue}
              >
                <Text style={styles.reportButtonText}>Report Issue</Text>
              </TouchableOpacity>
            </View>

            {retryCount >= maxRetries && (
              <Text style={styles.maxRetriesText}>
                Maximum retry attempts reached. Please restart the app or contact support.
              </Text>
            )}
          </View>
        </View>
      );
    }

    return children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  criticalContainer: {
    backgroundColor: '#FFF5F5',
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  criticalTitle: {
    color: '#FF6B6B',
  },
  message: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  errorDetails: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    marginBottom: 16,
    fontFamily: 'monospace',
    backgroundColor: '#F5F5F5',
    padding: 8,
    borderRadius: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#7C9A85',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  criticalButton: {
    backgroundColor: '#FF6B6B',
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  reportButton: {
    backgroundColor: '#E0E0E0',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  reportButtonText: {
    color: '#333333',
    fontSize: 16,
    fontWeight: '600',
  },
  maxRetriesText: {
    fontSize: 14,
    color: '#FF6B6B',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
