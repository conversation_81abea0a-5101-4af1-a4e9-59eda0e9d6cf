/**
 * Enhanced Messages Screen - Real-time Customer Communication with Aura Design System
 *
 * Component Contract:
 * - Displays customer's message conversations with providers using Aura design
 * - Shows conversation list with last message, unread counts, and real-time updates
 * - Supports navigation to individual chat screens with smooth transitions
 * - Integrates with backend messaging API and WebSocket for real-time features
 * - Implements typing indicators, message status, and push notifications
 * - Follows Aura design system, responsive design and WCAG 2.1 AA accessibility
 * - Enhanced UX with message search, conversation management, and offline support
 *
 * @version 3.0.0 - Enhanced with Aura Design System and Real-time Features
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { StyleSheet, FlatList, ActivityIndicator, RefreshControl,  } from 'react-native';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { HeaderHelpButton } from '../components/help';
import { Card } from '../components/atoms/Card';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';

// Enhanced UI Components following Aura Design System
import { EnhancedTouchTarget } from '../components/ui/EnhancedTouchTarget';
import { EnhancedScreenReader } from '../components/accessibility/EnhancedScreenReader';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import type { CustomerStackParamList } from '../navigation/types';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';
import { messagingService, Conversation } from '../services/messagingService';
import { createWebSocketService } from '../services/websocketService';

// Enhanced utilities and real-time features
import { trackInteraction } from '../utils/analytics';
import { usePerformance } from '../hooks/usePerformance';
import { useErrorHandling } from '../hooks/useErrorHandling';
import cacheService from '../services/cacheService';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';

// Helper function to generate avatar colors - using theme-aware colors
const getAvatarColor = (name: string, themeColors: any): string => {
  // Use theme-aware colors that work in both light and dark modes
  const avatarColors = [
    themeColors.sage400, // Primary sage
    themeColors.sage500, // Medium sage
    themeColors.sage600, // Darker sage
    themeColors.success || '#10B981', // Success green
    themeColors.warning || '#F59E0B', // Warning orange
    themeColors.info || '#3B82F6', // Info blue
    themeColors.sage300, // Light sage
  ];
  const index = name.charCodeAt(0) % avatarColors.length;
  return avatarColors[index];
};

// Helper function to format timestamps
const formatTimestamp = (timestamp: string): string => {
  const now = new Date();
  const messageTime = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes} min ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;

  return messageTime.toLocaleDateString();
};

// Extended conversation interface for UI display
interface ConversationDisplay extends Conversation {
  participantName: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  avatar?: string;
}

type MessagesScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;

export const MessagesScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createEnhancedStyles(colors);
  const navigation = useNavigation<MessagesScreenNavigationProp>();
  const { user } = useAuthStore();

  // Enhanced performance monitoring and error handling
  const { trackUserInteraction } = usePerformance();
  const { handleError, clearError } = useErrorHandling();

  const [conversations, setConversations] = useState<ConversationDisplay[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Enhanced real-time state management
  const [typingIndicators, setTypingIndicators] = useState<Record<string, boolean>>({});
  const [onlineStatus, setOnlineStatus] = useState<Record<string, boolean>>({});
  const [unreadCounts, setUnreadCounts] = useState<Record<string, number>>({});
  const [lastSeenTimestamps, setLastSeenTimestamps] = useState<Record<string, Date>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredConversations, setFilteredConversations] = useState<ConversationDisplay[]>([]);

  useEffect(() => {
    loadConversations();
    setupRealTimeUpdates();

    return () => {
      // Cleanup WebSocket connection
      cleanupRealTimeUpdates();
    };
  }, []);

  const setupRealTimeUpdates = async () => {
    try {
      const wsService = createWebSocketService({
        url: 'ws://192.168.2.65:8000/ws/messages/',
        reconnectAttempts: 5,
        reconnectInterval: 3000,
      });

      // Connect with auth token
      const { authToken } = useAuthStore.getState();
      await wsService.connect(authToken);

      // Listen for new messages
      wsService.on('chat_message', (data: any) => {
        handleNewMessage(data);
      });

      // Listen for conversation updates
      wsService.on('conversation_updated', (data: any) => {
        handleConversationUpdate(data);
      });

      // Listen for typing indicators
      wsService.on('typing_indicator', (data: any) => {
        handleTypingIndicator(data);
      });

    } catch (error) {
      console.error('Failed to setup real-time updates:', error);
    }
  };

  const cleanupRealTimeUpdates = () => {
    // WebSocket cleanup will be handled by the service
  };

  // Enhanced real-time message handling
  const handleNewMessage = useCallback((messageData: any) => {
    // Update conversation list with new message
    setConversations(prev =>
      prev.map(conv => {
        if (conv.id === messageData.conversation_id) {
          return {
            ...conv,
            lastMessage: messageData.content,
            timestamp: messageData.created_at,
            unreadCount: conv.unreadCount + 1,
          };
        }
        return conv;
      })
    );

    // Update unread counts
    setUnreadCounts(prev => ({
      ...prev,
      [messageData.conversation_id]: (prev[messageData.conversation_id] || 0) + 1
    }));

    // Show notification if app is in background
    // This would be handled by push notification service
  }, []);

  const handleConversationUpdate = useCallback((conversationData: any) => {
    // Update specific conversation
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationData.id
          ? { ...conv, ...conversationData }
          : conv
      )
    );
  }, []);

  const handleTypingIndicator = useCallback((typingData: any) => {
    const { conversation_id, user_id, is_typing } = typingData;

    setTypingIndicators(prev => ({
      ...prev,
      [conversation_id]: is_typing
    }));

    // Clear typing indicator after timeout
    if (is_typing) {
      setTimeout(() => {
        setTypingIndicators(prev => ({
          ...prev,
          [conversation_id]: false
        }));
      }, 3000);
    }
  }, []);

  const handleUserOnlineStatus = useCallback((statusData: any) => {
    const { user_id, is_online, last_seen } = statusData;

    setOnlineStatus(prev => ({
      ...prev,
      [user_id]: is_online
    }));

    if (last_seen) {
      setLastSeenTimestamps(prev => ({
        ...prev,
        [user_id]: new Date(last_seen)
      }));
    }
  }, []);

  // Enhanced search and filtering functionality
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredConversations(conversations);
      return;
    }

    const filtered = conversations.filter(conv =>
      conv.providerName.toLowerCase().includes(query.toLowerCase()) ||
      conv.lastMessage.toLowerCase().includes(query.toLowerCase())
    );

    setFilteredConversations(filtered);
  }, [conversations]);

  // Update filtered conversations when conversations change
  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearchChange(searchQuery);
    } else {
      setFilteredConversations(conversations);
    }
  }, [conversations, searchQuery, handleSearchChange]);

  // Enhanced conversation press handler with analytics
  const handleConversationPress = useCallback(async (conversation: ConversationDisplay) => {
    await trackUserInteraction('open_conversation', async () => {
      // Mark conversation as read
      setUnreadCounts(prev => ({
        ...prev,
        [conversation.id]: 0
      }));

      // Navigate to chat screen
      navigation.navigate('Chat', {
        providerId: conversation.providerId,
        providerName: conversation.providerName,
        conversationId: conversation.id,
        providerAvatar: conversation.avatar
      });
    });
  }, [navigation, trackUserInteraction]);

  const loadConversations = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get conversations from backend
      const response = await messagingService.getConversations(1, 20);

      // Transform backend conversations to match ConversationDisplay interface
      const transformedConversations: ConversationDisplay[] = response.conversations.map(conv => {
        const otherParticipant = conv.other_participant;
        const lastMessage = conv.last_message;

        return {
          ...conv, // Include all original conversation properties
          participantName: otherParticipant?.full_name || 'Unknown User',
          lastMessage: lastMessage?.content || 'No messages yet',
          timestamp: lastMessage?.created_at || new Date().toISOString(),
          unreadCount: conv.unread_count || 0,
          avatar: otherParticipant?.profile_image,
        };
      });

      setConversations(transformedConversations);
    } catch (err) {
      console.error('Error loading conversations:', err);
      setError('Failed to load conversations. Please try again.');

      // Show fallback empty state instead of crashing
      setConversations([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setError(null);
    await loadConversations();
    setRefreshing(false);
  };

  const handleConversationPress = async (conversation: ConversationDisplay) => {
    try {
      // Mark as read on backend
      await messagingService.markConversationAsRead(conversation.id);

      // Mark as read locally
      setConversations(prev =>
        prev.map(conv =>
          conv.id === conversation.id ? { ...conv, unreadCount: 0 } : conv,
        ),
      );

      // Navigate to conversation screen
      navigation.navigate('Conversation', {
        conversationId: conversation.id,
        participantName: conversation.participantName,
      });
    } catch (error) {
      console.error('Error marking conversation as read:', error);
      // Still navigate even if marking as read fails
      navigation.navigate('Conversation', {
        conversationId: conversation.id,
        participantName: conversation.participantName,
      });
    }
  };

  const formatLastMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    }
  };



  const renderConversation = ({ item }: { item: ConversationDisplay }) => {
    const hasUnread = item.unreadCount > 0;
    const isOnline = Math.random() > 0.5; // TODO: Implement real online status

    return (
      <TouchableOpacity
        onPress={() => handleConversationPress(item)}
        testID={`conversation-${item.id}`}
        accessibilityLabel={`Conversation with ${item.participantName}`}
        accessibilityHint="Tap to open chat"
        style={[styles.conversationItem, hasUnread && styles.unreadConversation]}
        activeOpacity={0.7}>
        <View style={styles.conversationContent}>
          {/* Avatar */}
          <View style={styles.avatarContainer}>
            <View style={[styles.avatar, { backgroundColor: getAvatarColor(item.participantName, colors) }]}>
              <Text style={styles.avatarText}>
                {item.participantName.charAt(0).toUpperCase()}
              </Text>
            </View>
            {/* Online indicator */}
            {isOnline && <View style={styles.onlineIndicator} />}
          </View>

            {/* Conversation Info */}
            <View style={styles.conversationInfo}>
              <View style={styles.conversationHeader}>
                <Text
                  style={[
                    styles.participantName,
                    hasUnread && styles.unreadText,
                  ]}
                  numberOfLines={1}>
                  {item.participantName}
                </Text>
                <Text style={styles.timestamp}>
                  {formatLastMessageTime(item.timestamp)}
                </Text>
              </View>

              <View style={styles.lastMessageContainer}>
                <Text
                  style={[styles.lastMessage, hasUnread && styles.unreadText]}
                  numberOfLines={2}>
                  {item.lastMessage}
                </Text>
                {hasUnread && (
                  <View style={styles.unreadBadge}>
                    <Text style={styles.unreadCount}>
                      {item.unreadCount > 99 ? '99+' : item.unreadCount}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Chevron */}
            <Ionicons
              name="chevron-forward"
              size={16}
              color={colors.text.tertiary}
              style={styles.chevron}
            />
          </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="chatbubbles-outline"
        size={64}
        color={colors.text.tertiary}
      />
      <Text style={styles.emptyTitle}>No Messages Yet</Text>
      <Text style={styles.emptyDescription}>
        Start a conversation with a service provider by booking a service or
        asking a question.
      </Text>
      <Button
        onPress={() => {
          /* TODO: Navigate to search */
        }}
        variant="primary"
        style={styles.findProvidersButton}>
        Find Providers
      </Button>
    </View>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle="dark-content"
      respectNotch={true}
      respectGestures={true}
      testID="messages-screen">
      {/* Header with actions */}
      <View style={styles.header}>
        <Text style={styles.title}>Messages</Text>
        <View style={styles.headerActions}>
          <HeaderHelpButton
            size="medium"
            testID="messages-help-button"
          />
          <TouchableOpacity
            style={styles.newMessageButton}
            testID="new-message-button"
            accessibilityLabel="Start new conversation"
            accessibilityRole="button">
            <Ionicons name="create-outline" size={24} color={colors.sage400} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Conversations List */}
      {loading && conversations.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.sage400} />
          <Text style={styles.loadingText}>Loading conversations...</Text>
        </View>
      ) : (
        <FlatList
          data={conversations}
          renderItem={renderConversation}
          keyExtractor={item => item.id}
          style={styles.conversationsList}
          contentContainerStyle={styles.conversationsContent}
          showsVerticalScrollIndicator={false}
          testID="conversations-list"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.sage400]}
              tintColor={colors.sage400}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error loading messages. Please try again.
          </Text>
        </View>
      )}
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => StyleSheet.create({

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    backgroundColor: colors.surface || colors.background?.primary || colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.sage100 || colors.border?.light || colors.border.light,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(8),
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: colors.text || colors.text.primary,
  },
  newMessageButton: {
    padding: getResponsiveSpacing(8),
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: colors.surface.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(40),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    marginTop: getResponsiveSpacing(16),
  },
  conversationsList: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  conversationsContent: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingTop: getResponsiveSpacing(8),
  },
  conversationItem: {
    backgroundColor: colors.surface.primary,
    borderRadius: getResponsiveSpacing(12),
    marginBottom: getResponsiveSpacing(8),
    padding: getResponsiveSpacing(16),
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  unreadConversation: {
    borderLeftWidth: 4,
    borderLeftColor: colors.sage400,
    backgroundColor: colors.surface.secondary,
  },
  conversationContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: getResponsiveSpacing(12),
  },
  avatar: {
    width: getResponsiveSpacing(48),
    height: getResponsiveSpacing(48),
    borderRadius: getResponsiveSpacing(24),
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: getResponsiveSpacing(12),
    height: getResponsiveSpacing(12),
    borderRadius: getResponsiveSpacing(6),
    backgroundColor: '#34D399',
    borderWidth: 2,
    borderColor: colors.background.primary,
  },
  conversationInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(8),
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(4),
  },
  participantName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
  },
  unreadText: {
    fontWeight: '700',
    color: colors.text.primary,
  },
  timestamp: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.tertiary,
  },
  lastMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  lastMessage: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    flex: 1,
    lineHeight: getResponsiveFontSize(20),
  },
  unreadBadge: {
    backgroundColor: colors.sage400,
    borderRadius: getResponsiveSpacing(10),
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(2),
    marginLeft: getResponsiveSpacing(8),
    minWidth: getResponsiveSpacing(20),
    alignItems: 'center',
  },
  unreadCount: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  chevron: {
    marginLeft: getResponsiveSpacing(8),
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(60),
    paddingHorizontal: getResponsiveSpacing(40),
  },
  emptyTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(8),
  },
  emptyDescription: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
    marginBottom: getResponsiveSpacing(24),
  },
  findProvidersButton: {
    minWidth: getResponsiveSpacing(140),
  },
  errorContainer: {
    backgroundColor: colors.error?.background || '#FEE2E2',
    padding: getResponsiveSpacing(16),
    margin: getResponsiveSpacing(20),
    borderRadius: getResponsiveSpacing(8),
  },
  errorText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.error?.text || '#DC2626',
    textAlign: 'center',
  },
});
