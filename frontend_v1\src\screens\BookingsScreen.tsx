/**
 * Enhanced Bookings Screen - Customer Booking Management with Aura Design System
 *
 * Component Contract:
 * - Displays customer's booking history with advanced filtering and Aura design
 * - Allows comprehensive booking management (cancel, reschedule, contact provider)
 * - Shows upcoming and past bookings with real-time status updates
 * - Integrates with backend booking API with proper error handling and caching
 * - Supports real-time booking updates via WebSocket connections
 * - Follows Aura design system, responsive design and WCAG 2.1 AA accessibility
 * - Implements proper loading states, offline support, and performance optimization
 * - Enhanced UX with smooth animations, haptic feedback, and intuitive interactions
 *
 * @version 3.0.0 - Enhanced with Aura Design System
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, Modal, Dimensions } from 'react-native';
import { StyleSheet, FlatList, ActivityIndicator, RefreshControl, Alert, <PERSON>rollView } from 'react-native';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { Card } from '../components/atoms/Card';
import { StoreImage } from '../components/molecules/StoreImage';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { HeaderHelpButton } from '../components/help';

// Enhanced UI Components following Aura Design System
import { EnhancedTouchTarget } from '../components/ui/EnhancedTouchTarget';
import { EnhancedScreenReader } from '../components/accessibility/EnhancedScreenReader';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'react-native';
// import { BookingCard } from '../components/bookings/BookingCard';
// import { EmptyState } from '../components/ui/EmptyState';
// import { LoadingSpinner } from '../components/ui/LoadingSpinner';
// import { ErrorBoundary } from '../components/ui/ErrorBoundary';
// MegaMenu removed - REC-RESP-001: Using thumb-friendly bottom navigation only
import { useTheme } from '../contexts/ThemeContext';
import { useI18n } from '../contexts/I18nContext';
import { useAuthStore } from '../store/authSlice';
import type { Booking } from '../features/booking/types';
import type { CustomerStackParamList } from '../navigation/types';
import { useBookingsStore } from '../store/bookingsSlice';
import { bookingService, type BookingStatus } from '../services/bookingService';
// import { enhancedWebSocketService } from '../services/enhancedWebSocketService';
// import { cacheService } from '../services/cacheService';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';
import { formatDate, formatTime, formatCurrency } from '../utils/canadianFormats';

// Enhanced utilities and performance monitoring
import { trackInteraction } from '../utils/analytics';
import { usePerformance } from '../hooks/usePerformance';
import { useErrorHandling } from '../hooks/useErrorHandling';
import cacheService from '../services/cacheService';
// Simple toast utility function will be defined below

interface FilterTab {
  id: string;
  name: string;
  count?: number;
  icon?: string;
}

interface BookingAction {
  id: string;
  label: string;
  icon: string;
  color: string;
  action: (booking: Booking) => void;
}

interface BookingModalData {
  visible: boolean;
  booking: Booking | null;
  type: 'details' | 'cancel' | 'reschedule' | 'contact';
}

type BookingsScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;

const { width: screenWidth } = Dimensions.get('window');

// Simple toast utility
const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  Alert.alert(
    type === 'error' ? 'Error' : type === 'success' ? 'Success' : 'Info',
    message,
    [{ text: 'OK' }]
  );
};

export const BookingsScreen: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const { user } = useAuthStore();
  const styles = createEnhancedStyles(colors);
  const navigation = useNavigation<BookingsScreenNavigationProp>();

  // Enhanced performance monitoring and error handling
  const { trackUserInteraction } = usePerformance();
  const { handleError, clearError } = useErrorHandling();

  // Enhanced state management
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'status' | 'provider'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [modalData, setModalData] = useState<BookingModalData>({
    visible: false,
    booking: null,
    type: 'details'
  });

  // Real-time updates
  const [wsConnected, setWsConnected] = useState(false);

  const {
    fetchBookings: storeFetchBookings,
    cancelBooking: storeCancelBooking,
    getBookingsByStatus,
  } = useBookingsStore();

  // Enhanced filter tabs with counts and icons
  const filterTabs: FilterTab[] = useMemo(() => [
    {
      id: 'all',
      name: t('bookings.filters.all', 'All'),
      icon: 'list-outline',
      count: bookings.length
    },
    {
      id: 'upcoming',
      name: t('bookings.filters.upcoming', 'Upcoming'),
      icon: 'calendar-outline',
      count: bookings.filter(b => ['pending', 'confirmed'].includes(b.status)).length
    },
    {
      id: 'in_progress',
      name: t('bookings.filters.inProgress', 'In Progress'),
      icon: 'play-circle-outline',
      count: bookings.filter(b => b.status === 'in_progress').length
    },
    {
      id: 'completed',
      name: t('bookings.filters.completed', 'Completed'),
      icon: 'checkmark-circle-outline',
      count: bookings.filter(b => b.status === 'completed').length
    },
    {
      id: 'cancelled',
      name: t('bookings.filters.cancelled', 'Cancelled'),
      icon: 'close-circle-outline',
      count: bookings.filter(b => b.status === 'cancelled').length
    },
  ], [bookings, t]);

  // Enhanced data loading
  useEffect(() => {
    loadBookings();
  }, []);

  useEffect(() => {
    loadBookings();
  }, [selectedFilter, sortBy, sortOrder]);

  const loadBookings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the improved booking service with proper backend integration
      let bookingResponse;

      if (selectedFilter === 'all') {
        bookingResponse = await bookingService.getBookings();
      } else if (selectedFilter === 'upcoming') {
        bookingResponse = await bookingService.getUpcomingBookings();
      } else {
        // Filter by specific status
        bookingResponse = await bookingService.getBookings({
          status: selectedFilter as any
        });
      }

      // Apply sorting to the fetched bookings
      const sortedBookings = sortBookings(bookingResponse.results);
      setBookings(sortedBookings);

      // Update the store with the fetched bookings
      useBookingsStore.getState().bookings = bookingResponse.results;

    } catch (error) {
      console.error('Failed to load bookings:', error);
      setError(error instanceof Error ? error.message : 'Failed to load bookings');
      showToast('Failed to load bookings', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Enhanced sorting function
  const sortBookings = useCallback((bookingsToSort: Booking[]) => {
    return [...bookingsToSort].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime();
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'provider':
          comparison = a.providerName.localeCompare(b.providerName);
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [sortBy, sortOrder]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
  };

  // Enhanced booking actions
  const getBookingActions = useCallback((booking: Booking): BookingAction[] => {
    const actions: BookingAction[] = [];

    // View details action (always available)
    actions.push({
      id: 'details',
      label: t('bookings.actions.viewDetails', 'View Details'),
      icon: 'information-circle-outline',
      color: colors.primary,
      action: (booking) => setModalData({ visible: true, booking, type: 'details' })
    });

    // Contact provider action
    actions.push({
      id: 'contact',
      label: t('bookings.actions.contactProvider', 'Contact Provider'),
      icon: 'chatbubble-outline',
      color: colors.success,
      action: (booking) => setModalData({ visible: true, booking, type: 'contact' })
    });

    // Status-specific actions
    if (['pending', 'confirmed'].includes(booking.status)) {
      actions.push({
        id: 'reschedule',
        label: t('bookings.actions.reschedule', 'Reschedule'),
        icon: 'calendar-outline',
        color: colors.warning,
        action: (booking) => setModalData({ visible: true, booking, type: 'reschedule' })
      });

      actions.push({
        id: 'cancel',
        label: t('bookings.actions.cancel', 'Cancel'),
        icon: 'close-circle-outline',
        color: colors.error,
        action: (booking) => setModalData({ visible: true, booking, type: 'cancel' })
      });
    }

    // Rebook action for completed bookings
    if (booking.status === 'completed') {
      actions.push({
        id: 'rebook',
        label: t('bookings.actions.rebook', 'Book Again'),
        icon: 'repeat-outline',
        color: colors.primary,
        action: (booking) => handleRebook(booking)
      });
    }

    return actions;
  }, [colors, t]);

  // Enhanced booking action handlers
  const handleCancelBooking = useCallback(async (booking: Booking) => {
    try {
      setLoading(true);

      await trackUserInteraction('cancel_booking', async () => {
        await storeCancelBooking(booking.id);

        // Update local state immediately for better UX
        setBookings(prev => prev.map(b =>
          b.id === booking.id ? { ...b, status: 'cancelled' } : b
        ));

        // Update cache
        await cacheService.invalidate('bookings');

        showToast(t('bookings.messages.cancelSuccess', 'Booking cancelled successfully'), 'success');
        setModalData({ visible: false, booking: null, type: 'details' });
      });

    } catch (error) {
      console.error('Failed to cancel booking:', error);
      handleError(error as Error);
      showToast(
        t('bookings.messages.cancelError', 'Failed to cancel booking. Please try again.'),
        'error'
      );
    } finally {
      setLoading(false);
    }
  }, [trackUserInteraction, handleError, t, storeCancelBooking]);

  const handleRescheduleBooking = async (booking: Booking, newDate: Date, newTime: string) => {
    try {
      setLoading(true);

      // Format the date and time for the API
      const formattedDate = newDate.toISOString().split('T')[0];

      // Use the booking service to reschedule the booking
      const rescheduledBooking = await bookingService.rescheduleBooking(
        booking.id,
        formattedDate,
        newTime
      );

      // Update the local bookings list
      setBookings(prevBookings =>
        prevBookings.map(b =>
          b.id === booking.id
            ? { ...b, scheduled_datetime: rescheduledBooking.scheduled_datetime }
            : b
        )
      );

      showToast('Booking rescheduled successfully', 'success');
      setModalData({ visible: false, booking: null, type: 'details' });

    } catch (error) {
      console.error('Failed to reschedule booking:', error);
      showToast(
        t('bookings.messages.rescheduleError', 'Failed to reschedule booking. Please try again.'),
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRebook = useCallback(async (booking: Booking) => {
    await trackUserInteraction('rebook_service', async () => {
      navigation.navigate('ServiceDetails', {
        serviceId: booking.serviceId,
        providerId: booking.providerId,
        rebook: true,
        previousBooking: booking
      });
    });
  }, [navigation, trackUserInteraction]);

  const handleContactProvider = useCallback(async (booking: Booking) => {
    await trackUserInteraction('contact_provider', async () => {
      navigation.navigate('Chat', {
        providerId: booking.providerId,
        bookingId: booking.id,
        providerName: booking.providerName
      });
    });
  }, [navigation, trackUserInteraction]);

  // handleMegaMenuNavigate removed - REC-RESP-001: Using thumb-friendly bottom navigation only

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return colors.success?.text || '#10B981';
      case 'pending':
        return colors.warning?.text || '#F59E0B';
      case 'completed':
        return colors.sage400;
      case 'cancelled':
        return colors.error?.text || '#EF4444';
      case 'in_progress':
        return colors.info?.text || '#3B82F6';
      default:
        return colors.text.secondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'completed':
        return 'checkmark-done-circle';
      case 'cancelled':
        return 'close-circle';
      case 'in_progress':
        return 'play-circle';
      default:
        return 'help-circle';
    }
  };

  // Enhanced filtering and search logic
  const filteredBookings = useMemo(() => {
    let filtered = [...bookings];

    // Apply status filter
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'upcoming') {
        filtered = filtered.filter(booking =>
          ['pending', 'confirmed'].includes(booking.status)
        );
      } else {
        filtered = filtered.filter(booking => booking.status === selectedFilter);
      }
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(booking =>
        booking.serviceName.toLowerCase().includes(query) ||
        booking.providerName.toLowerCase().includes(query) ||
        booking.status.toLowerCase().includes(query) ||
        booking.location?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [bookings, selectedFilter, searchQuery]);

  // Enhanced empty state logic
  const getEmptyStateConfig = () => {
    if (loading) {
      return null;
    }

    if (error) {
      return {
        icon: 'alert-circle-outline',
        title: t('bookings.empty.error.title', 'Unable to load bookings'),
        description: t('bookings.empty.error.description', 'Please check your connection and try again'),
        actionLabel: t('bookings.empty.error.action', 'Retry'),
        onAction: loadBookings
      };
    }

    if (searchQuery.trim() && filteredBookings.length === 0) {
      return {
        icon: 'search-outline',
        title: t('bookings.empty.search.title', 'No bookings found'),
        description: t('bookings.empty.search.description', 'Try adjusting your search terms'),
        actionLabel: t('bookings.empty.search.action', 'Clear Search'),
        onAction: () => setSearchQuery('')
      };
    }

    if (selectedFilter === 'all' && bookings.length === 0) {
      return {
        icon: 'calendar-outline',
        title: t('bookings.empty.all.title', 'No bookings yet'),
        description: t('bookings.empty.all.description', 'Start exploring services to make your first booking'),
        actionLabel: t('bookings.empty.all.action', 'Browse Services'),
        onAction: () => navigation.navigate('Search')
      };
    }

    if (filteredBookings.length === 0) {
      const filterName = filterTabs.find(tab => tab.id === selectedFilter)?.name || selectedFilter;
      return {
        icon: 'calendar-outline',
        title: t('bookings.empty.filter.title', `No ${filterName.toLowerCase()} bookings`),
        description: t('bookings.empty.filter.description', `You don't have any ${filterName.toLowerCase()} bookings`),
        actionLabel: t('bookings.empty.filter.action', 'View All Bookings'),
        onAction: () => setSelectedFilter('all')
      };
    }

    return null;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const renderFilterTab = ({ item }: { item: FilterTab }) => (
    <TouchableOpacity
      style={[
        styles.filterTab,
        selectedFilter === item.id && styles.activeFilterTab,
      ]}
      onPress={() => setSelectedFilter(item.id)}
      testID={`filter-${item.id}`}
      accessibilityLabel={`Filter by ${item.name}`}
      accessibilityRole="button">
      <Text
        style={[
          styles.filterTabText,
          selectedFilter === item.id && styles.activeFilterTabText,
        ]}>
        {item.name}
      </Text>
      {item.count !== undefined && (
        <View style={styles.countBadge}>
          <Text style={styles.countText}>{item.count}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderBooking = ({ item }: { item: Booking }) => (
    <Card style={styles.bookingCard}>
      <View style={styles.bookingHeader}>
        <View style={styles.bookingImageContainer}>
          <StoreImage
            providerId={item.provider_id}
            providerName={item.provider_name}
            category={item.service_category}
            size="small"
            testID={`booking-image-${item.id}`}
          />
        </View>
        <View style={styles.bookingInfo}>
          <Text style={styles.providerName} numberOfLines={1}>
            {item.provider_name}
          </Text>
          <Text style={styles.serviceName} numberOfLines={1}>
            {item.service_name}
          </Text>
        </View>
        <View style={styles.statusContainer}>
          <Ionicons
            name={getStatusIcon(item.status) as any}
            size={16}
            color={getStatusColor(item.status)}
          />
          <Text
            style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status_display}
          </Text>
        </View>
      </View>

      <View style={styles.bookingDetails}>
        <View style={styles.dateTimeContainer}>
          <View style={styles.dateTimeItem}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={colors.text.tertiary}
            />
            <Text style={styles.dateTimeText}>
              {formatDate(item.scheduled_datetime)}
            </Text>
          </View>
          <View style={styles.dateTimeItem}>
            <Ionicons
              name="time-outline"
              size={16}
              color={colors.text.tertiary}
            />
            <Text style={styles.dateTimeText}>
              {formatTime(item.scheduled_datetime)}
            </Text>
          </View>
        </View>

        <View style={styles.durationPriceContainer}>
          <Text style={styles.durationText}>{item.duration_display}</Text>
          <Text style={styles.priceText}>${item.total_amount}</Text>
        </View>
      </View>

      {item.customer_notes && (
        <View style={styles.notesContainer}>
          <Text style={styles.notesLabel}>Notes:</Text>
          <Text style={styles.notesText} numberOfLines={2}>
            {item.customer_notes}
          </Text>
        </View>
      )}

      <View style={styles.bookingActions}>
        {item.can_be_cancelled && (
          <Button
            onPress={() => handleCancelBooking(item)}
            variant="secondary"
            style={styles.actionButton}
            testID={`cancel-${item.id}`}>
            Cancel
          </Button>
        )}
        {item.can_be_rescheduled && (
          <Button
            onPress={() => handleRescheduleBooking(item)}
            variant="outline"
            style={styles.actionButton}
            testID={`reschedule-${item.id}`}>
            Reschedule
          </Button>
        )}
        {item.status === 'completed' && (
          <Button
            onPress={() => {
              navigation.navigate('LeaveReview', {
                bookingId: item.id,
                providerName: item.provider_name,
                serviceName: item.service_name,
              });
            }}
            variant="primary"
            style={styles.actionButton}
            testID={`review-${item.id}`}>
            Leave Review
          </Button>
        )}
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="calendar-outline"
        size={64}
        color={colors.text.tertiary}
      />
      <Text style={styles.emptyTitle}>{t('bookings.emptyState.title')}</Text>
      <Text style={styles.emptyDescription}>
        {selectedFilter === 'all'
          ? t('bookings.emptyState.description.all')
          : t('bookings.emptyState.description.filtered', { filter: selectedFilter })}
      </Text>
      <Button
        onPress={() => {
          /* TODO: Navigate to search */
        }}
        variant="primary"
        style={styles.bookNowButton}>
        {t('bookings.emptyState.action')}
      </Button>
    </View>
  );

  // Enhanced render functions following Aura design system
  const renderEnhancedHeader = () => (
    <View style={styles.enhancedHeader}>
      <LinearGradient
        colors={[colors.primary?.default || '#5A7A63', colors.primary?.light || '#A5C7AC']}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <Text style={styles.enhancedHeaderTitle}>My Bookings</Text>
          <EnhancedTouchTarget
            style={styles.headerActionButton}
            onPress={() => navigation.navigate('Search')}
            accessibilityLabel="Book new service"
            accessibilityHint="Navigate to search to book a new service"
            testID="book-new-service"
            minimumSize={44}
            showTouchFeedback={true}
          >
            <Ionicons name="add" size={24} color="#FFFFFF" />
          </EnhancedTouchTarget>
        </View>
      </LinearGradient>
    </View>
  );

  const renderEnhancedFilterTabs = () => (
    <View style={styles.filterTabsContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterTabsScroll}
      >
        {filterTabs.map((tab) => (
          <EnhancedTouchTarget
            key={tab.id}
            style={[
              styles.filterTab,
              selectedFilter === tab.id && styles.filterTabActive
            ]}
            onPress={() => setSelectedFilter(tab.id)}
            accessibilityLabel={`Filter by ${tab.name}`}
            accessibilityHint={`Show ${tab.count} ${tab.name.toLowerCase()} bookings`}
            testID={`filter-${tab.id}`}
            minimumSize={44}
            showTouchFeedback={true}
          >
            <Ionicons
              name={tab.icon as any}
              size={20}
              color={selectedFilter === tab.id ? '#FFFFFF' : colors.text?.secondary}
            />
            <Text style={[
              styles.filterTabText,
              selectedFilter === tab.id && styles.filterTabTextActive
            ]}>
              {tab.name}
            </Text>
            {tab.count > 0 && (
              <View style={[
                styles.filterTabBadge,
                selectedFilter === tab.id && styles.filterTabBadgeActive
              ]}>
                <Text style={[
                  styles.filterTabBadgeText,
                  selectedFilter === tab.id && styles.filterTabBadgeTextActive
                ]}>
                  {tab.count}
                </Text>
              </View>
            )}
          </EnhancedTouchTarget>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <EnhancedScreenReader
        region={{
          id: 'bookings-main',
          label: 'Bookings Screen',
          role: 'main',
          description: 'View and manage your service bookings',
          live: 'polite'
        }}
        announceOnMount="Bookings screen loaded. View and manage your service bookings."
        skipToContent={true}
        landmarkNavigation={true}
        autoDescribe={true}
      >
        {/* Enhanced Header */}
        {renderEnhancedHeader()}

        {/* Enhanced Filter Tabs */}
        {renderEnhancedFilterTabs()}

        {/* Enhanced Bookings List */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary?.default || '#5A7A63']}
              tintColor={colors.primary?.default || '#5A7A63'}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {loading && bookings.length === 0 ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary?.default} />
              <Text style={styles.loadingText}>Loading your bookings...</Text>
            </View>
          ) : filteredBookings.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <View style={styles.emptyStateIcon}>
                <Ionicons name="calendar-outline" size={48} color={colors.text?.tertiary} />
              </View>
              <Text style={styles.emptyStateTitle}>
                {selectedFilter === 'all' ? 'No bookings yet' : `No ${selectedFilter} bookings`}
              </Text>
              <Text style={styles.emptyStateSubtitle}>
                {selectedFilter === 'all'
                  ? 'Book your first service to see it here'
                  : `You don't have any ${selectedFilter} bookings at the moment`
                }
              </Text>
              {selectedFilter === 'all' && (
                <EnhancedTouchTarget
                  style={styles.bookNowButton}
                  onPress={() => navigation.navigate('Search')}
                  accessibilityLabel="Book your first service"
                  accessibilityHint="Navigate to search to find and book services"
                  testID="book-first-service"
                  minimumSize={44}
                  showTouchFeedback={true}
                >
                  <Text style={styles.bookNowButtonText}>Book Now</Text>
                </EnhancedTouchTarget>
              )}
            </View>
          ) : (
            <View style={styles.bookingsListContainer}>
              {filteredBookings.map((booking, index) => (
                <EnhancedBookingCard
                  key={booking.id}
                  booking={booking}
                  onPress={() => handleBookingPress(booking)}
                  onCancel={() => handleCancelBooking(booking)}
                  onReschedule={() => handleRescheduleBooking(booking)}
                  onContact={() => handleContactProvider(booking)}
                  onRebook={() => handleRebook(booking)}
                  style={[styles.bookingCard, index === filteredBookings.length - 1 && styles.lastBookingCard]}
                />
              ))}
            </View>
          )}
        </ScrollView>
      </EnhancedScreenReader>
    </View>
  );
      testID="bookings-screen"
      accessibilityLabel="My bookings screen"
      accessibilityRole="main">
      {/* Header */}
      <View
        style={styles.header}
        accessibilityRole="header"
        accessibilityLabel="Bookings screen header">
        <View style={styles.menuButton}>
          {/* Menu button removed - using bottom navigation */}
        </View>
        <Text
          style={styles.title}
          accessibilityRole="header"
          accessibilityLevel={1}>
          {t('bookings.title')}
        </Text>
        <HeaderHelpButton
          size="medium"
          testID="bookings-help-button"
          accessibilityLabel="Get help"
          accessibilityHint="Double tap to access help and support options"
        />
      </View>

      {/* Filter Tabs */}
      <View style={styles.filtersContainer}>
        <FlatList
          data={filterTabs}
          renderItem={renderFilterTab}
          keyExtractor={item => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersList}
        />
      </View>

      {/* Bookings List */}
      {loading && bookings.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.sage400} />
          <Text style={styles.loadingText}>Loading bookings...</Text>
        </View>
      ) : (
        <FlatList
          data={bookings}
          renderItem={renderBooking}
          keyExtractor={item => item.id}
          style={styles.bookingsList}
          contentContainerStyle={styles.bookingsContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.sage400]}
              tintColor={colors.sage400}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error loading bookings. Please try again.
          </Text>
        </View>
      )}

      {/* MegaMenu removed - using bottom navigation */}
    </SafeAreaScreen>
  );
};

// Enhanced Booking Card Component following Aura Design System
interface EnhancedBookingCardProps {
  booking: Booking;
  onPress: () => void;
  onCancel: () => void;
  onReschedule: () => void;
  onContact: () => void;
  onRebook: () => void;
  style?: any;
}

const EnhancedBookingCard: React.FC<EnhancedBookingCardProps> = ({
  booking,
  onPress,
  onCancel,
  onReschedule,
  onContact,
  onRebook,
  style
}) => {
  const { colors } = useTheme();
  const styles = createEnhancedStyles(colors);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return colors.success?.default || '#10B981';
      case 'pending':
        return colors.warning?.default || '#F59E0B';
      case 'completed':
        return colors.info?.default || '#3B82F6';
      case 'cancelled':
        return colors.error?.default || '#DC2626';
      default:
        return colors.text?.tertiary || '#9CA3AF';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'completed':
        return 'checkmark-done-circle';
      case 'cancelled':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const formatBookingDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays > 1 && diffDays <= 7) return `In ${diffDays} days`;
    if (diffDays < -1 && diffDays >= -7) return `${Math.abs(diffDays)} days ago`;

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  return (
    <EnhancedTouchTarget
      style={[styles.enhancedBookingCard, style]}
      onPress={onPress}
      accessibilityLabel={`Booking with ${booking.providerName} on ${booking.date}`}
      accessibilityHint="Tap to view booking details"
      testID={`booking-card-${booking.id}`}
      minimumSize={120}
      showTouchFeedback={true}
      enableVisualFeedback={true}
    >
      <View style={styles.bookingCardContent}>
        {/* Booking Status Indicator */}
        <View style={[styles.bookingStatusIndicator, { backgroundColor: getStatusColor(booking.status) }]} />

        {/* Provider Info */}
        <View style={styles.bookingProviderSection}>
          <View style={styles.bookingProviderAvatar}>
            {booking.providerAvatar ? (
              <Image
                source={{ uri: booking.providerAvatar }}
                style={styles.bookingProviderImage}
              />
            ) : (
              <Text style={styles.bookingProviderInitials}>
                {booking.providerName.split(' ').map(n => n[0]).join('').toUpperCase()}
              </Text>
            )}
          </View>
          <View style={styles.bookingProviderInfo}>
            <Text style={styles.bookingProviderName} numberOfLines={1}>
              {booking.providerName}
            </Text>
            <Text style={styles.bookingServiceName} numberOfLines={1}>
              {booking.serviceName}
            </Text>
          </View>

          {/* Booking Status Badge */}
          <View style={[styles.bookingStatusBadge, { backgroundColor: getStatusColor(booking.status) }]}>
            <Ionicons name={getStatusIcon(booking.status) as any} size={12} color="#FFFFFF" />
            <Text style={styles.bookingStatusText}>
              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
            </Text>
          </View>
        </View>

        {/* Booking Details */}
        <View style={styles.bookingDetailsSection}>
          <View style={styles.bookingDateTimeContainer}>
            <Ionicons name="calendar-outline" size={16} color={colors.text?.secondary} />
            <Text style={styles.bookingDateTime}>
              {formatBookingDate(booking.date)} at {booking.time}
            </Text>
          </View>

          <View style={styles.bookingLocationContainer}>
            <Ionicons name="location-outline" size={16} color={colors.text?.secondary} />
            <Text style={styles.bookingLocation} numberOfLines={1}>
              {booking.location || 'Location TBD'}
            </Text>
          </View>

          <View style={styles.bookingPriceContainer}>
            <Text style={styles.bookingPrice}>
              {formatCurrency(booking.price)}
            </Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.bookingActions}>
          {booking.status === 'confirmed' && (
            <>
              <EnhancedTouchTarget
                style={styles.bookingActionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onReschedule();
                }}
                accessibilityLabel="Reschedule booking"
                minimumSize={36}
                showTouchFeedback={true}
              >
                <Ionicons name="calendar" size={16} color={colors.primary?.default} />
              </EnhancedTouchTarget>

              <EnhancedTouchTarget
                style={styles.bookingActionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onCancel();
                }}
                accessibilityLabel="Cancel booking"
                minimumSize={36}
                showTouchFeedback={true}
              >
                <Ionicons name="close" size={16} color={colors.error?.default} />
              </EnhancedTouchTarget>
            </>
          )}

          <EnhancedTouchTarget
            style={styles.bookingActionButton}
            onPress={(e) => {
              e.stopPropagation();
              onContact();
            }}
            accessibilityLabel="Contact provider"
            minimumSize={36}
            showTouchFeedback={true}
          >
            <Ionicons name="chatbubble" size={16} color={colors.primary?.default} />
          </EnhancedTouchTarget>

          {booking.status === 'completed' && (
            <EnhancedTouchTarget
              style={styles.bookingActionButton}
              onPress={(e) => {
                e.stopPropagation();
                onRebook();
              }}
              accessibilityLabel="Book again"
              minimumSize={36}
              showTouchFeedback={true}
            >
              <Ionicons name="repeat" size={16} color={colors.primary?.default} />
            </EnhancedTouchTarget>
          )}
        </View>
      </View>
    </EnhancedTouchTarget>
  );
};

const createEnhancedStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },

  // Enhanced Header Styles following Aura design system
  enhancedHeader: {
    position: 'relative',
    zIndex: 10,
  },
  headerGradient: {
    paddingBottom: getResponsiveSpacing(4),
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(4),
    paddingTop: getResponsiveSpacing(6),
    paddingBottom: getResponsiveSpacing(4),
  },
  enhancedHeaderTitle: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: '#FFFFFF',
    flex: 1,
  },
  headerActionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Enhanced Filter Tabs Styles
  filterTabsContainer: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    paddingVertical: getResponsiveSpacing(3),
    borderBottomWidth: 1,
    borderBottomColor: colors?.border?.primary || '#E0E0E0',
  },
  filterTabsScroll: {
    paddingHorizontal: getResponsiveSpacing(4),
    gap: getResponsiveSpacing(2),
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(4),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: 25,
    backgroundColor: colors?.background?.secondary || '#F5F5F5',
    gap: getResponsiveSpacing(2),
    minHeight: 44,
  },
  filterTabActive: {
    backgroundColor: colors?.primary?.default || '#5A7A63',
  },
  filterTabText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors?.text?.secondary || '#666666',
  },
  filterTabTextActive: {
    color: '#FFFFFF',
  },
  filterTabBadge: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  filterTabBadgeActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  filterTabBadgeText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
  },
  filterTabBadgeTextActive: {
    color: '#FFFFFF',
  },

  // Scroll View and Content Styles
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: getResponsiveSpacing(6),
  },

  // Loading and Empty States
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(8),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
    marginTop: getResponsiveSpacing(2),
  },
  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(8),
    paddingHorizontal: getResponsiveSpacing(4),
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors?.background?.secondary || '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(3),
  },
  emptyStateTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
    marginBottom: getResponsiveSpacing(1),
    textAlign: 'center',
  },
  emptyStateSubtitle: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: getResponsiveSpacing(4),
  },
  bookNowButton: {
    backgroundColor: colors?.primary?.default || '#5A7A63',
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(3),
    borderRadius: 25,
  },
  bookNowButtonText: {
    color: '#FFFFFF',
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
  },

  // Bookings List Styles
  bookingsListContainer: {
    paddingHorizontal: getResponsiveSpacing(4),
    paddingTop: getResponsiveSpacing(4),
  },
  bookingCard: {
    marginBottom: getResponsiveSpacing(3),
  },
  lastBookingCard: {
    marginBottom: getResponsiveSpacing(6),
  },

  // Enhanced Booking Card Styles
  enhancedBookingCard: {
    backgroundColor: colors?.background?.primary || '#FFFFFF',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    overflow: 'hidden',
  },
  bookingCardContent: {
    padding: getResponsiveSpacing(4),
  },
  bookingStatusIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
  },
  bookingProviderSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(3),
    marginTop: getResponsiveSpacing(2),
  },
  bookingProviderAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors?.primary?.light || '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getResponsiveSpacing(3),
  },
  bookingProviderImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  bookingProviderInitials: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors?.primary?.default || '#5A7A63',
  },
  bookingProviderInfo: {
    flex: 1,
  },
  bookingProviderName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors?.text?.primary || '#000000',
    marginBottom: 2,
  },
  bookingServiceName: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
  },
  bookingStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  bookingStatusText: {
    fontSize: getResponsiveFontSize(10),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  bookingDetailsSection: {
    marginBottom: getResponsiveSpacing(3),
    gap: getResponsiveSpacing(2),
  },
  bookingDateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(2),
  },
  bookingDateTime: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
  },
  bookingLocationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(2),
  },
  bookingLocation: {
    fontSize: getResponsiveFontSize(14),
    color: colors?.text?.secondary || '#666666',
    flex: 1,
  },
  bookingPriceContainer: {
    alignItems: 'flex-start',
  },
  bookingPrice: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '700',
    color: colors?.text?.primary || '#000000',
  },
  bookingActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: getResponsiveSpacing(2),
  },
  bookingActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors?.background?.secondary || '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    backgroundColor: colors.surface || colors.background?.primary || colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.sage100 || colors.border?.light || colors.border.light,
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: colors.text || colors.text.primary,
  },
  menuButton: {
    padding: getResponsiveSpacing(8),
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: 'transparent',
  },
  filtersContainer: {
    backgroundColor: colors.surface || colors.background?.primary || colors.background.primary,
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.sage100 || colors.border?.light || colors.border.light,
  },
  filtersList: {
    paddingHorizontal: getResponsiveSpacing(20),
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(8),
    marginRight: getResponsiveSpacing(12),
    borderRadius: getResponsiveSpacing(20),
    backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border.light,
    minHeight: getMinimumTouchTarget(),
  },
  activeFilterTab: {
    backgroundColor: colors.sage400,
    borderColor: colors.sage400,
  },
  filterTabText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.text.secondary,
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
  countBadge: {
    marginLeft: getResponsiveSpacing(6),
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(10),
    backgroundColor: colors.background.tertiary,
  },
  countText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: colors.text.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(40),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    marginTop: getResponsiveSpacing(16),
  },
  bookingsList: {
    flex: 1,
  },
  bookingsContent: {
    padding: getResponsiveSpacing(20),
  },
  bookingCard: {
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(16),
  },
  bookingHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(12),
  },
  bookingImageContainer: {
    marginRight: getResponsiveSpacing(12),
  },
  bookingImagePlaceholder: {
    width: getResponsiveSpacing(50),
    height: getResponsiveSpacing(50),
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: '#7C9A85',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bookingImageText: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
    color: '#FFFFFF',
  },
  bookingInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  providerName: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(4),
  },
  serviceName: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(12),
    backgroundColor: colors.background.tertiary,
  },
  statusText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    marginLeft: getResponsiveSpacing(4),
  },
  bookingDetails: {
    marginBottom: getResponsiveSpacing(12),
  },
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getResponsiveSpacing(8),
  },
  dateTimeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateTimeText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(6),
  },
  durationPriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  durationText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.tertiary,
  },
  priceText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.sage400,
  },
  notesContainer: {
    marginBottom: getResponsiveSpacing(12),
    padding: getResponsiveSpacing(12),
    backgroundColor: colors.background.tertiary,
    borderRadius: getResponsiveSpacing(8),
  },
  notesLabel: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors.text.tertiary,
    marginBottom: getResponsiveSpacing(4),
  },
  notesText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    lineHeight: getResponsiveFontSize(20),
  },
  bookingActions: {
    flexDirection: 'row',
    gap: getResponsiveSpacing(8),
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(60),
    paddingHorizontal: getResponsiveSpacing(40),
  },
  emptyTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(8),
  },
  emptyDescription: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
    marginBottom: getResponsiveSpacing(24),
  },
  bookNowButton: {
    minWidth: 140,
  },
  errorContainer: {
    backgroundColor: colors.error?.background || '#FEE2E2',
    padding: getResponsiveSpacing(16),
    margin: getResponsiveSpacing(20),
    borderRadius: getResponsiveSpacing(8),
  },
  errorText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.error?.text || '#DC2626',
    textAlign: 'center',
  },
});
